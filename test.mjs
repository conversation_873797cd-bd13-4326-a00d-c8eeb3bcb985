import fetch from 'node-fetch';

// fetch('http://172.16.10.5:31378/api/user/userDetail')
fetch('http://127.0.0.1:1787/api/user/userDetail')
  .then(response => {
    console.log('Status Code:', response.status);
    // 打印出跨域访问控制响应头
    console.log('Access-Control-Allow-Origin:', response.headers.get('Access-Control-Allow-Origin'));
    return response.text();
  })
  .then(text => {
    console.log(text);
  })
  .catch(err => {
    console.error('Error fetching the URL:', err);
  });
  