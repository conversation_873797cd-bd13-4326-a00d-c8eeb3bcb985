接下来，我们需要从百行FRAI001接口中提取出来以下指标，以下分别是特征名称，特征编码，以及对应在百行返回json中的字段

36个月内存在M2+的逾期	overdue_6d_36m	overdue60D_36M
24个月内存在M1+的逾期	overdue_30d_24m	overdue30D_24M
12个月内出现过15天以上的逾期	overdue_15d_12m	overdue15D_12M
24个月内出现过贷款首期即逾期，且逾期时间超过7天	first_vverdue_7d_24m	firstOverdue7D_24M
36个月内出现逾期金额大于等于5000元且逾期时间M1+	large_amt_overdue_30d_36m	largeAmtOverdue30D_36M
180天内实际放款的非银机构数（除去银行以及信用卡中心）大于等于3家	multi_quasi_fin3_180d	multiQuasiFin3_180D
180天内贷前查询的机构数大于等于8家	mult_query8_180d	multiQuery8_180D
30天内贷前查询的机构数大于等于5家	multi_query5_30d	multiQuery5_30D
90天内贷前查询的机构数大于等于6家	multi_query6_90d	multiQuery6_90D
3天内贷前查询的机构数大于等于3家	multi_query3_3d	multiQuery3_3D

下面我会给你一个返回的json例子
{"maxOverdueStatus": "-1", "multiPartyLoanStatus": "-1", "mobileMaxOverdueStatus": "1", "contactMobileMaxOverdueStatus": "-1", "mobileMultiPartyLoanStatus": "0", "contactMobileMultiPartyLoanStatus": "-1", "p2pEscapeDebtStatus": "-2", "supremeCourtExecutedStatus": "-2", "overdue15D_12M": "-1", "firstOverdue7D_24M": "-1", "largeAmtOverdue30D_36M": "-1", "overdue30D_24M": "-1", "overdue60D_36M": "-1", "mobileOverdue60D_36M": "1", "mobileOverdue30D_24M": "1", "mobileOverdue15D_12M": "0", "mobileFirstOverdue7D_24M": "0", "mobileLargeAmtOverdue30D_36M": "1", "contactMobileOverdue60D_36M": "-1", "contactMobileOverdue15D_12M": "-1", "contactMobileOverdue30D_24M": "-1", "contactMobileFirstOverdue7D_24M": "-1", "contactMobileLargeAmtOverdue30D_36M": "-1", "mobileMultiPartyLoan3_90D": "0", "mobileMultiQuasiFin3_180D": "0", "mobileMultiSubsidy2_180D": "0", "mobileMultiQuery8_180D": "0", "mobileMultiQuery5_30D": "0", "mobileMultiQuery6_90D": "0", "mobileMultiQuery3_3D": "0", "contactMobileMultiPartyLoan3_90D": "-1", "contactMobileMultiQuasiFin3_180D": "-1", "contactMobileMultiSubsidy2_180D": "-1", "contactMobileMultiQuery8_180D": "-1", "contactMobileMultiQuery5_30D": "-1", "contactMobileMultiQuery6_90D": "-1", "contactMobileMultiQuery3_3D": "-1", "multiQuery3_3D": "-1", "multiQuery5_30D": "-1", "multiQuery6_90D": "-1", "multiQuery8_180D": "-1", "multiPartyLoan3_90D": "-1", "multiQuasiFin3_180D": "-1", "multiSubsidy2_180D": "-1"


BH10099 百行综合风险指数
BH10004 证件号码风险指数
BH10005 手机号码风险指数
BH10024 还款压力风险等级
BH10026 近期履约风险等级
BH10027 远期履约风险等级
BH10028 借新还旧风险等级
BH10029 骗贷风险等级
BH10033 历史履约风险等级
BH10038 历史首逾风险等级

接下来我们要针对百行的接口 BH_AF_02_1030，提取以下相关指标，
BH10099 百行综合风险指数: comp_risk_index
BH10004 证件号码风险指数: id_risk_index
BH10005 手机号码风险指数: phone_risk_index
BH10024 还款压力风险等级: repay_pressure_level
BH10026 近期履约风险等级: short_term_perf_level
BH10027 远期履约风险等级: long_term_perf_level
BH10028 借新还旧风险等级: rollover_risk_level
BH10029 骗贷风险等级: fraud_risk_level
BH10033 历史履约风险等级: hist_perf_level
BH10038 历史首逾风险等级: hist_first_default_level
其对应的json数据格式如下，注意取的值时其中的value后，然后这个返回的json数据可能不存在对应的指标，那边不存在的值默认给0就好
{"code": "000", "message": "操作成功", "data": {"batNo": "2024102611300001", "result": 1, "hasSystemError": 0, "fields": [{"id": "BH10004", "name": "证件号码风险指数", "value": "100"}, {"id": "BH10005", "name": "手机号码风险指数", "value": "100"}, {"id": "BH10006", "name": "证件号码关联手机号码指数", "value": "0"}, {"id": "BH10009", "name": "手机号码关联证件号码指数", "value": "0"}, {"id": "BH10013", "name": "证件号码关联风险比率", "value": "0"}, {"id": "BH10015", "name": "手机号码关联风险比率", "value": "0"}, {"id": "BH10016", "name": "近期信贷需求强度", "value": "0"}, {"id": "BH10017", "name": "远期信贷需求强度", "value": "0"}, {"id": "BH10018", "name": "近期多头行为强度", "value": "0"}, {"id": "BH10019", "name": "远期多头行为强度", "value": "0"}, {"id": "BH10022", "name": "多机构贷款风险等级", "value": "0"}, {"id": "BH10023", "name": "多头借贷风险等级", "value": "0"}, {"id": "BH10024", "name": "还款压力风险等级", "value": "0"}, {"id": "BH10025", "name": "负债能力比率", "value": "0.40"}, {"id": "BH10026", "name": "近期履约风险等级", "value": "0"}, {"id": "BH10027", "name": "远期履约风险等级", "value": "1"}, {"id": "BH10028", "name": "借新还旧风险等级", "value": "0"}, {"id": "BH10029", "name": "骗贷风险等级", "value": "0"}, {"id": "BH10031", "name": "中期还款压力等级", "value": "0"}, {"id": "BH10032", "name": "远期还款压力等级", "value": "0"}, {"id": "BH10033", "name": "历史履约风险等级", "value": "0"}, {"id": "BH10034", "name": "近期多头履约风险等级", "value": "0"}, {"id": "BH10035", "name": "远期多头履约风险等级", "value": "0"}, {"id": "BH10036", "name": "历史多头履约风险等级", "value": "0"}, {"id": "BH10037", "name": "远期首逾风险等级", "value": "0"}, {"id": "BH10038", "name": "历史首逾风险等级", "value": "0"}, {"id": "BH10099", "name": "风险指数", "value": "69.6"}], "receiveTime": "2024-10-26 11:10:02", "riskView": "风险画像", "viewValue": "手机号码命中风险;证件号码命中风险"}}






location /authsandbox {
    proxy_pass  https://authsandbox.boxincredit.com;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}

UPPB018 综合逾期风险评分: overall_overdue_risk_score
UPPB006 套现风险等级: cashout_risk_level
UPPC185 消费能力指数: consumption_ability_index

UPPC087 近1个月短时异地交易次数: short_term_remote_txn_count_1m
UPPC088 近3个月短时异地交易次数: short_term_remote_txn_count_3m
UPPC113 近12个月超大额交易力度占比: large_txn_ratio_12m
UPPC451 近1个月午夜交易天数: midnight_txn_days_1m
UPPC452 近3个月午夜交易天数: midnight_txn_days_3m

UPPC237 近1个月午夜消费频度: midnight_spending_freq_1m
UPPC238 近3个月午夜消费频度: midnight_spending_freq_3m
UPPC277 近1个月博彩类消费频度: gambling_spending_freq_1m
UPPC273 近1个月博彩类消费力度: gambling_spending_intensity_1m
UPPC278 近3个月博彩类消费频度: gambling_spending_freq_3m
UPPC274 近3个月博彩类消费力度: gambling_spending_intensity_3m

UPPC352 近1个月午夜取现交易力度: midnight_cashout_intensity_1m
UPPC356 近1个月午夜取现交易频度: midnight_cashout_freq_1m
UPPC353 近3个月午夜取现交易力度: midnight_cashout_intensity_3m
UPPC357 近3个月午夜取现交易频度: midnight_cashout_freq_3m
UPPD066 近1个月失败交易天数: failed_txn_days_1m
UPPD067 近3个月失败交易天数: failed_txn_days_3m

UPPD074 近1个月资金不足交易天数: insufficient_funds_days_1m
UPPD075 近3个月资金不足交易天数: insufficient_funds_days_3m
UPPD037 近1个月信用卡金额超限交易频度: credit_overlimit_freq_1m
UPPD038 近3个月信用卡金额超限交易频度: credit_overlimit_freq_3m
UPPJ001 近3个月疑似套现月份数: suspected_cashout_months_3m
UPPJ002 近3个月疑似套现力度: suspected_cashout_intensity_3m
UPPJ003 近3个月疑似套现频度: suspected_cashout_freq_3m
UPPJ004 近6个月总套现力度: total_cashout_intensity_6m


{'code': '0000', 'message': 'success', 'data': {'data': '[{"UPPD040":"0","UPPB018":"568","UPPW201":"\\"null\\"","UPPW200":"\\"null\\"","UPPD036":"0","UPPD037":"0","UPPD038":"0","UPPC185":"\\"null\\"","UPPD032":"2","UPPB015":"0","UPPB014":"0","UPPG027":"3","UPPB012":"\\"null\\"","UPPW215":"\\"null\\"","UPPB006":"\\"null\\"","UPPW214":"\\"null\\"","UPPB009":"\\"null\\"","UPPC452":"0","UPPD026":"0","UPPC451":"0","UPPC296":"0","UPPG019":"2300","UPPC454":"0","UPPC211":"0","UPPC453":"0","UPPJ004":"0","UPPJ003":"0","UPPJ002":"0","UPPJ001":"0","UPPC292":"0","UPPD010":"0","UPPD011":"1","UPPD012":"1","UPPD097":"4800","UPPC280":"0","UPPD093":"2","UPPD007":"1","UPPD008":"4","UPPD009":"0","UPPC276":"0","UPPD003":"80","UPPD004":"5100","UPPC274":"0","UPPC273":"0","UPPC278":"0","UPPD001":"0","UPPC277":"0","UPPC272":"0","UPPD081":"2","UPPA006":"\\"null\\"","UPPA004":"1","UPPW129":"\\"null\\"","UPPC149":"0","UPPC144":"0","UPPC143":"0","UPPC383":"0","UPPA002":"2","UPPC148":"0","UPPA003":"0","UPPC147":"0","UPPC268":"0","UPPD077":"2","UPPD078":"0","UPPA001":"2","UPPC145":"0","UPPD073":"0","UPPD074":"0","UPPD075":"0","UPPW136":"\\"null\\"","UPPD069":"4","UPPC495":"-0.23","UPPC494":"0.57","UPPD105":"2","UPPC493":"-1.00","UPPD066":"0","UPPD067":"0","UPPD101":"2","UPPD061":"0","UPPG054":"0","UPPC090":"0","UPPC403":"0","UPPC088":"0","UPPC087":"0","UPPG046":"0","UPPD056":"1","UPPG049":"0","UPPG044":"0","UPPC238":"0","UPPC359":"0","UPPC237":"0","UPPC353":"0","UPPB022":"369","UPPC352":"0","UPPD048":"0","UPPB021":"230","UPPC357":"0","UPPC356":"0","UPPD044":"0","UPPB025":"702","UPPC113":"0.00"}]', 'errorCode': '0', 'requestCode': 'YKRZ_USER_508833635076542464', 'responseCode': '95d214fd0b78438f8c7c43d1b5e3ca13'}}

gambling
hit        银行卡涉赌命中风险: card_gambling_risk  
reg_count  银行卡涉赌注册网站数: card_gambling_website_count
balance    银行卡涉赌金额: card_gambling_amount
login_time 银行卡涉赌网址最近登录时间: card_gambling_last_login_time
reg_time   银行卡涉赌网址最近注册时间: card_gambling_last_reg_time

pyramid
hit        银行卡涉诈骗命中风险: card_fraud_risk
reg_count  银行卡涉诈骗注册网站数: card_fraud_website_count
balance    银行卡涉诈骗金额: card_fraud_amount
login_time 银行卡涉诈骗网址最近登录时间: card_fraud_last_login_time
reg_time   银行卡涉诈骗网址最近注册时间: card_fraud_last_reg_time

根据以上信息我们来完成net_risk.py文件中的代码，完成对应的接口数据提取，首先要完成对应的返回结果检查，然后提取对应的数据指标，返回接口没有命中时候返回结果如下{"chargeCode": "0", "code": "1", "data": {"fe01bc587b6e4f19ff06380f0f235429": {"gambling": {"hit": 0}, "pyramid": {"hit": 0}}}, "errorMsg": "", "status": 0, "transNo": "1ffc9ccb2286e5ceca593305af16b913"}
有命中时候，返回如下
{"chargeCode": "0", "code": "1", "data": {"147c9adbbc9910267c3bdf2bdd90580a": {"gambling": {"reg_count": 2, "hit": 1, "balance": 0, "login_time": 0, "reg_time": 0, "is_agent": 0}, "pyramid": {"reg_count": 1, "hit": 1, "balance": 1, "login_time": 0, "reg_time": 0, "is_agent": 0}}}, "errorMsg": "", "status": 0, "transNo": "2ea0d2b819ddeed1fcca24f9659a87b8"}


