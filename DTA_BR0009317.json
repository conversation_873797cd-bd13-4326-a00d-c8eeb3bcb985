{"code": "00", "swift_number": "3034679_20250103145233_3003", "ApplyApprovalEvaluate": {"m1": {"lqt_interday": "1", "bank": {"lqt_interday": "1", "eqt_interday": "1", "query_orgnum": "1"}, "eqt_interday": "1", "nbank": {"lqt_interday": "1", "eqt_interday": "1", "query_orgnum": "1"}, "m3": {"bt_ratio_change1": "0.5000", "nn_ratio": "0.5000", "nt_ratio_change1": "0.5000", "tt_ratio": "0.5000", "bb_ratio": "0.5000"}, "query_orgnum": "1", "m6": {"bt_ratio_change1": "0.5000", "nn_ratio": "0.5000", "nt_ratio_change1": "0.5000", "tt_ratio": "0.5000", "bb_ratio": "0.5000"}, "m24": {"bt_ratio_change1": "0.5000", "nn_ratio": "0.5000", "nt_ratio_change1": "0.5000", "tt_ratio": "0.5000", "bb_ratio": "0.5000"}, "m12": {"bt_ratio_change1": "0.5000", "nn_ratio": "0.5000", "nt_ratio_change1": "0.5000", "tt_ratio": "0.5000", "bb_ratio": "0.5000"}}, "m3": {"lqt_interday": "1", "bank": {"lqt_interday": "1", "eqt_interday": "1", "pass_orgnum": "1", "query_orgnum": "1", "passratio": "0.5000", "rej_orgnum": "1"}, "eqt_interday": "1", "nbank": {"lqt_interday": "1", "eqt_interday": "1", "pass_orgnum": "1", "query_orgnum": "1", "passratio": "0.5000", "rej_orgnum": "1"}, "pass_orgnum": "1", "query_orgnum": "1", "m6": {"bt_ratio_change1": "0.5000", "nn_ratio": "0.5000", "nt_ratio_change1": "0.5000", "tt_ratio": "0.5000", "bb_ratio": "0.5000"}, "passratio": "0.5000", "rej_orgnum": "1", "m24": {"bt_ratio_change1": "0.5000", "nn_ratio": "0.5000", "nt_ratio_change1": "0.5000", "tt_ratio": "0.5000", "bb_ratio": "0.5000"}, "m12": {"bt_ratio_change1": "0.5000", "nn_ratio": "0.5000", "nt_ratio_change1": "0.5000", "tt_ratio": "0.5000", "bb_ratio": "0.5000"}}, "m6": {"lqt_interday": "1", "bank": {"lqt_interday": "1", "eqt_interday": "1", "pass_orgnum": "1", "query_orgnum": "1", "passratio": "0.5000", "rej_orgnum": "1"}, "eqt_interday": "1", "nbank": {"lqt_interday": "1", "eqt_interday": "1", "pass_orgnum": "1", "query_orgnum": "1", "passratio": "0.5000", "rej_orgnum": "1"}, "pass_orgnum": "1", "query_orgnum": "1", "passratio": "0.5000", "rej_orgnum": "1", "m24": {"bt_ratio_change1": "0.5000", "nn_ratio": "0.5000", "nt_ratio_change1": "0.5000", "tt_ratio": "0.5000", "bb_ratio": "0.5000"}, "m12": {"bt_ratio_change1": "0.5000", "nn_ratio": "0.5000", "nt_ratio_change1": "0.5000", "tt_ratio": "0.5000", "bb_ratio": "0.5000"}}, "m24": {"lqt_interday": "1", "bank": {"lqt_interday": "1", "eqt_interday": "1", "rej": {"l_interday": "1", "e_interday": "1"}, "pass_orgnum": "1", "query_orgnum": "1", "national": {"rej": {"l_interday": "1", "e_interday": "1"}, "pass_orgnum": "1", "rej_orgnum": "1"}, "passratio": "0.5000", "rej_orgnum": "1", "region": {"rej": {"l_interday": "1", "e_interday": "1"}, "pass_orgnum": "1", "rej_orgnum": "1"}}, "eqt_interday": "1", "nbank": {"lqt_interday": "1", "eqt_interday": "1", "else_rel": {"rej": {"l_interday": "1", "e_interday": "1"}, "pass_orgnum": "1", "rej_orgnum": "1"}, "else_pdl": {"rej": {"l_interday": "1", "e_interday": "1"}, "pass_orgnum": "1", "rej_orgnum": "1"}, "sloan": {"rej": {"l_interday": "1", "e_interday": "1"}, "pass_orgnum": "1", "rej_orgnum": "1"}, "autofin": {"rej": {"l_interday": "1", "e_interday": "1"}, "pass_orgnum": "1", "rej_orgnum": "1"}, "pass_orgnum": "1", "rej_orgnum": "1", "ins": {"rej": {"l_interday": "1", "e_interday": "1"}, "pass_orgnum": "1", "rej_orgnum": "1"}, "top": {"rej": {"l_interday": "1", "e_interday": "1"}, "pass_orgnum": "1", "rej_orgnum": "1"}, "rej": {"l_interday": "1", "e_interday": "1"}, "query_orgnum": "1", "else_cons": {"rej": {"l_interday": "1", "e_interday": "1"}, "pass_orgnum": "1", "rej_orgnum": "1"}, "passratio": "0.5000", "cons": {"rej": {"l_interday": "1", "e_interday": "1"}, "pass_orgnum": "1", "rej_orgnum": "1"}, "trans": {"rej": {"l_interday": "1", "e_interday": "1"}, "pass_orgnum": "1", "rej_orgnum": "1"}}, "rej": {"bank": {"region_orgnum_ratio": "0.5000", "national_orgnum_ratio": "0.5000", "orgnum_ratio": "0.5000"}, "l_interday": "1", "e_interday": "1", "nbank": {"cons_orgnum_ratio": "0.5000", "else_rel_orgnum_ratio": "0.5000", "else_pdl_orgnum_ratio": "0.5000", "else_cons_orgnum_ratio": "0.5000", "trans_orgnum_ratio": "0.5000", "orgnum_ratio": "0.5000", "autofin_orgnum_ratio": "0.5000", "sloan_orgnum_ratio": "0.5000", "ins_orgnum_ratio": "0.5000", "top_orgnum_ratio": "0.5000"}}, "pass_orgnum": "1", "query_orgnum": "1", "passratio": "0.5000", "rej_orgnum": "0.5000"}, "m12": {"lqt_interday": "1", "bank": {"lqt_interday": "1", "eqt_interday": "1", "pass_orgnum": "1", "query_orgnum": "1", "national": {"pass_orgnum": "1", "rej_orgnum": "1"}, "passratio": "0.5000", "rej_orgnum": "1", "region": {"pass_orgnum": "1", "rej_orgnum": "1"}}, "eqt_interday": "1", "nbank": {"lqt_interday": "1", "eqt_interday": "1", "else_rel": {"pass_orgnum": "1", "rej_orgnum": "1"}, "else_pdl": {"pass_orgnum": "1", "rej_orgnum": "1"}, "sloan": {"pass_orgnum": "1", "rej_orgnum": "1"}, "autofin": {"pass_orgnum": "1", "rej_orgnum": "1"}, "pass_orgnum": "1", "rej_orgnum": "1", "ins": {"pass_orgnum": "1", "rej_orgnum": "1"}, "top": {"pass_orgnum": "1", "rej_orgnum": "1"}, "query_orgnum": "1", "else_cons": {"pass_orgnum": "1", "rej_orgnum": "1"}, "passratio": "0.5000", "cons": {"pass_orgnum": "1", "rej_orgnum": "1"}, "trans": {"pass_orgnum": "1", "rej_orgnum": "1"}}, "pass_orgnum": "1", "query_orgnum": "1", "passratio": "0.5000", "rej_orgnum": "1", "m24": {"bt_ratio_change1": "0.5000", "nn_ratio": "0.5000", "nt_ratio_change1": "0.5000", "rej": {"bank": {"national_orgnum_ratio_change1": "0.5000", "orgnum_ratio_change1": "0.5000", "region_orgnum_ratio_change1": "0.5000"}, "nbank": {"top_orgnum_ratio_change1": "0.5000", "cons_orgnum_ratio_change1": "0.5000", "else_cons_orgnum_ratio_change1": "0.5000", "else_rel_orgnum_ratio_change1": "0.5000", "autofin_orgnum_ratio_change1": "0.5000", "ins_orgnum_ratio_change1": "0.5000", "else_pdl_orgnum_ratio_change1": "0.5000", "sloan_orgnum_ratio_change1": "0.5000", "orgnum_ratio_change1": "0.5000", "trans_orgnum_ratio_change1": "0.5000"}}, "tt_ratio": "0.5000", "bb_ratio": "0.5000"}}}, "DataStrategy": {"strategy_version": "", "product_type": "100099", "strategy_id": "DTA_BR0009317", "product_name": "预置_借贷意向衍生特征-核批", "scene": "lend"}, "Flag": {"applyapprovalevaluate": "1", "datastrategy": "1"}}