package rszd;

import Verify.util.AESUtils;
import Verify.util.MD5Utils;
import com.alibaba.fastjson.JSONObject;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.URL;
import java.net.URLConnection;
import java.util.HashMap;
import java.util.Map;

public class demo {



    private static String merchantId = "";
    private static String key = "";
    private static String url ="";
    public static void main(String[] args) throws Exception {

        info();
    }

    /***
     * @throws Exception
     */
    public static void info() throws Exception {

        Map<String,Object> map =new HashMap<>();
        map.put("idCard","");
        map.put("type","");
        map.put("encrypt","");


        String jsonString = JSONObject.toJSONString(map);
        jsonString = AESUtils.encrypt(jsonString, MD5Utils.encryptToUpperCase(key).substring(8,24));

        sendPost(url + "/verify/analyticMark", jsonString,String.valueOf(System.currentTimeMillis()));

    }

    /**
     * 向指定 URL 发送POST方法的请求
     *
     * @param url
     *            发送请求的 URL
     * @param param
     *            请求参数
     */
    public static void sendPost(String url,String param,String requestTime) throws Exception {

        String sign = MD5Utils.encrypt(MD5Utils.encrypt(requestTime + key));
        Map<String, String> headers = new HashMap<String, String>();
        headers.put("merchantId", merchantId);
        headers.put("requestTime",requestTime);
        headers.put("sign", sign);

        PrintWriter out = null;
        BufferedReader in = null;
        String result = "";
        try {
            URL realUrl = new URL(url);
            // 打开和URL之间的连接
            URLConnection conn = realUrl.openConnection();
            //conn.setRequestProperty("Content-type", "application/json");
            conn.setRequestProperty("Content-type", "text/plain;charset=UTF-8;");
            // 设置通用的请求属性
            conn.setRequestProperty("connection", "Keep-Alive");
            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);

            conn.setDoInput(true);
            // 添加消息头
            for (String key : headers.keySet()) {
                conn.addRequestProperty(key, headers.get(key));
            }
            // 获取URLConnection对象对应的输出流
            out = new PrintWriter(conn.getOutputStream());
            // 发送请求参数
            out.print(param);
            // flush输出流的缓冲
            out.flush();
            // 定义BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
            System.out.println("result:" + result);
        } catch (Exception e) {
            System.out.println("发送 POST 请求出现异常！" + e);
            e.printStackTrace();
        }
        // 使用finally块来关闭输出流、输入流
        finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
    }
}
