-Djava.security.properties=/Users/<USER>/DataGripProjects/database-client/custom.java.security

mvn -s "/Users/<USER>/.m2/settings_txy.xml" deploy:deploy-file -DgroupId=com.newland -DartifactId=jrbdp-transfer -Dversion=3.2.6-SNAPSHOT -Dpackaging=jar -Dfile=/Users/<USER>/data/jrbdp-bak4/jrbdp-transfer-3.2.6.jar -Durl=http://**************:8081/repository/maven-releases/ -DrepositoryId=maven-releases


 pip3 download -d /root/path pandas  -i https://pypi.mirrors.ustc.edu.cn/simple/


pip3 install greenlet-2.0.2-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
pip3 install numpy-1.24.3-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
pip3 install pypinyin-0.48.0-py2.py3-none-any.whl   
pip3 install six-1.16.0-py2.py3-none-any.whl
pip3 install pytz-2023.3-py2.py3-none-any.whl
pip3 install python_dateutil-2.8.2-py2.py3-none-any.whl
pip3 install typing_extensions-4.5.0-py3-none-any.whl
pip3 install tzdata-2023.3-py2.py3-none-any.whl
pip3 install SQLAlchemy-2.0.11-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
pip3 install pandas-2.0.1-cp38-cp38-manylinux_2_17_x86_64.manylinux2014_x86_64.whl

mvn -s "/Users/<USER>/.m2/settings_txy.xml" deploy:deploy-file -DgroupId=com.newland -DartifactId=jrbdp-transfer -Dversion=3.2.6 -Dpackaging=jar -Dfile=/Users/<USER>/data/jrbdp-bak4/jrbdp-transfer-3.2.6.jar -Durl=http://**************:8081/repository/maven-releases/ -DrepositoryId=nexus -Dusername=admin -Dpassword=e705605a-333d-4eb2-9ee9-019be33617d4

## 动态转发 登录后保持终端开启
ssh -N -D 127.0.0.1:10810 xiaozhaodong@*************

## 使用代理链接目标服务器
ssh -o ProxyCommand='nc -X 5 -x 127.0.0.1:10810 %h %p' -l cs-web *************

# 使用代理动态转发
ssh -D 127.0.0.1:10811 -o ProxyCommand='nc -X 5 -x 127.0.0.1:10810 %h %p' -l cs-web -p 22 *************

# 本地端口转发 登录后保持终端开启 navicat连接本地 10812即可
ssh -o ProxyCommand='nc -X 5 -x 127.0.0.1:10810 %h %p' -L 127.0.0.1:10812:*************:3306 cs-web@*************

ps -fu $USER | grep dynamic_forward.sh | awk '{print $2}


docker run --detach --publish 8443:443 --publish 8888:8080 --publish 8222:22 --name gitlab --restart unless-stopped --volume /usr/local/gitlab/etc:/etc/gitlab --volume /usr/local/gitlab/log:/var/log/gitlab --volume /usr/local/gitlab/data:/var/opt/gitlab --privileged=true gitlab/gitlab-ce:latest

docker run --detach \
    --hostname ************* \
    --publish 8443:443 --publish 8080:8080 --publish 8022:22 \
    --name gitlab \
    --restart always \
    --volume /srv/gitlab/config:/etc/gitlab \
    --volume /srv/gitlab/logs:/var/log/gitlab \
    --volume /srv/gitlab/data:/var/opt/gitlab \
    gitlab/gitlab-ce:latest  


docker run --detach \
  --publish 8443:443 \
  --publish 8080:80 \
  --publish 8222:22 \
  --name gitlab \
  --restart unless-stopped \
  --volume /data/gitlab/config:/etc/gitlab \
  --volume /data/gitlab/logs:/var/log/gitlab \
  --volume /data/gitlab/data:/var/opt/gitlab \
  --privileged=true \
  gitlab/gitlab-ce:latest

http://*************:8080/xiaozhaodong/smartcreditweb.git

docker run -d \
  --name mysql \
  -p 3306:3306 \
  -v /data/mysql/config:/etc/mysql/conf.d \
  -v /data/mysql/data:/var/lib/mysql \
  -e MYSQL_ROOT_PASSWORD=root@2024 \
  mysql:5.7.22

docker run -d \
  --name redis \
  -p 7001:6379 \
  -v /data/redis/data:/data \
  -e REDIS_PASSWORD=redis@2024 \
  redis:5.0.12 \
  redis-server --requirepass redis@2024

sudo chown -R 1001:1001 /data/mongodb/data
docker run -d \
  --name mongodb \
  -p 27017:27017 \
  -v /data/mongodb/data:/bitnami/mongodb/data \
  -e MONGODB_ROOT_PASSWORD=mongodb@2024 \
  bitnami/mongodb:4.0.13


docker run -d \
  --name rmqnamesrv \
  -p 9876:9876 \
  apache/rocketmq:4.9.7 \
  sh mqnamesrv

docker run -d \
  --name rmqbroker \
  -p 10911:10911 \
  -p 10909:10909 \
  -e NAMESRV_ADDR=rmqnamesrv:9876 \
  apache/rocketmq:4.9.7 \
  sh mqbroker

# 本地运行
docker run -d \
  --name credit-engine \
  -p 8082:8082 \
  -v /Users/<USER>/data/credit-engine/logs:/opt/server/credit-engine/logs \
  -v /Users/<USER>/data/credit-engine/MLModleFile:/opt/server/credit-engine/MLModleFile \
  -v /Users/<USER>/data/credit-engine/riskWarn:/opt/server/credit-engine/riskWarn \
  credit-engine:2.6.0

# 远程运行
docker run -d \
  --name credit-engine \
  -p 8082:8082 \
  -v /data/credit-engine/logs:/opt/server/credit-engine/logs \
  -v /data/credit-engine/MLModleFile:/opt/server/credit-engine/MLModleFile \
  -v /data/credit-engine/riskWarn:/opt/server/credit-engine/riskWarn \
  -v /data/credit-engine/externalLib:/opt/server/credit-engine/externalLib \
  -v /data/credit-engine/phbTestFile:/opt/server/credit-engine/phbTestFile \
  -v /data/credit-engine/testFile:/opt/server/credit-engine/testFile \
  credit-engine:2.6.0


docker run -d \
  --name credit-consumer \
  -p 8080:8080 \
  -v /Users/<USER>/data/credit-consumer/logs:/opt/server/credit-consumer/logs \
  credit-consumer:2.6.0

docker run -d \
  --name credit-consumer \
  -p 8083:8083 \
  -v /data/credit-consumer/logs:/opt/server/credit-consumer/logs \
  credit-consumer:2.6.0

docker run -d \
  --name risk-insight \
  -p 1786:1786 \
  -v /data/risk-insight/logs:/data/risk-insight/application/logs \
  -v /data/risk-insight/MLModleFile:/data/risk-insight/application/storage \
  risk-insight:1.0.0

docker run -d \
  --name risk-insight \
  -p 1786:1786 \
  risk-insight:1.0.0


docker build -t credit-engine:2.6.0 .
docker save credit-engine:2.6.0 -o credit-engine.tar

docker build -t risk-insight:1.0.0 .
docker save risk-insight:1.0.0 -o risk-insight.tar

# worker-service本地启动
docker build -t worker-service:latest -f services/worker_service/Dockerfile . 
docker run -d --name worker-service -p 5555:5555 \
    -e WORKER_TYPE=all \
    -v /Users/<USER>/data/deep-risk-rag/worker-service/config:/app/config \
    -v /Users/<USER>/data/deep-risk-rag/worker-service/logs:/app/logs \
    -v /Users/<USER>/data/deep-risk-rag/worker-service/uploads:/app/data/uploads \
    -v /Users/<USER>/data/deep-risk-rag/worker-service/prompts:/app/prompts \
    worker-service:latest

docker build -t embedding-service:latest -f services/embedding_service/Dockerfile .


docker run -d \
  --name embedding-service  \
  -p 8004:8004 \
  -v /Users/<USER>/data/deep-risk-rag/embedding-service/logs:/app/logs \
  -v /Users/<USER>/data/deep-risk-rag/embedding-service/cache:/app/cache \
  -v /Users/<USER>/data/deep-risk-rag/embedding-service/models:/app/models \
  -v /Users/<USER>/data/deep-risk-rag/embedding-service/config:/app/config \
  embedding-service:latest

dcker run -d \
  --name embedding-service  \
  --gpus all \
  -p 8004:8004 \
  -v /Users/<USER>/data/deep-risk-rag/embedding-service/logs:/app/logs \
  -v /Users/<USER>/data/deep-risk-rag/embedding-service/cache:/app/cache \
  -v /Users/<USER>/data/deep-risk-rag/embedding-service/models:/app/models \
  -v /Users/<USER>/data/deep-risk-rag/embedding-service/config:/app/config \
  embedding-service:latest

docker run -d --name chroma -p 8001:8000 -v /Users/<USER>/data/deep-risk-rag/chroma-service/data:/data chromadb/chroma:latest 

docker run -d --name chroma -p 8010:8000 -v /data/deep-risk-rag/chroma/data:/data chromadb/chroma:latest 

docker build -t deep-service:latest -f services/deep_service/Dockerfile .

docker run -d --name deep-service -p 8000:8000 \
    -v /Users/<USER>/data/deep-risk-rag/deep-service/config:/app/config \
    -v /Users/<USER>/data/deep-risk-rag/deep-service/logs:/app/logs \
    -v /Users/<USER>/data/deep-risk-rag/deep-service/uploads:/app/data/uploads \
    -v /Users/<USER>/data/deep-risk-rag/deep-service/prompts:/app/prompts \
    deep-service:latest


docker run -d --name deep-service -p 8011:8000 \
    -v /data/deep-risk-rag/deep-service/config:/app/config \
    -v /data/deep-risk-rag/deep-service/logs:/app/logs \
    -v /data/deep-risk-rag/uploads:/app/data/uploads \
    -v /data/deep-risk-rag/deep-service/prompts:/app/prompts \
    deep-service:latest

curl https://api.deepseek.com/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-507373769c964852a6f4a54c5b816729" \
  -d '{
        "model": "deepseek-reasoner",
        "messages": [
          {"role": "system", "content": "You are a helpful assistant."},
          {"role": "user", "content": "你好!"}
        ],
        "stream": false
      }'


# static=🌏 国外网站, Global, US, HK, JP, TW, SG, Final, direct, img-url= https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Global.png

# https://raw.githubusercontent.com/DivineEngine/Profiles/master/Quantumult/Filter/Global.list, tag=🌍 国外网站, force-policy= 🌏 国外网站, enabled=true




# 0 30  68  128 328 128 30 #求和这一排数量
# 3 5   8    12  24 12 5

# 60        40  16  4 -1

# import java.io.IOException;
# import java.io.InputStream;
# import java.io.OutputStream;
# import java.net.InetAddress;
# import java.net.ServerSocket;
# import java.net.Socket;

# public class SocksProxyServer {
#     private static final int SOCKS_VERSION = 5;
#     private static final int BUF_SIZE = 4096;

#     private final InetAddress gatewayAddress;
#     private final int gatewayPort;

#     public SocksProxyServer(InetAddress gatewayAddress, int gatewayPort) {
#         this.gatewayAddress = gatewayAddress;
#         this.gatewayPort = gatewayPort;
#     }

#     public void start(int port) throws IOException {
#         ServerSocket serverSocket = new ServerSocket(port);
#         System.out.println("SOCKS5 proxy server started on port " + port);

#         while (true) {
#             Socket clientSocket = serverSocket.accept();
#             new Thread(() -> {
#                 try {
#                     handleClient(clientSocket);
#                 } catch (IOException e) {
#                     e.printStackTrace();
#                 }
#             }).start();
#         }
#     }

#     private void handleClient(Socket clientSocket) throws IOException {
#         System.out.println("Accepted connection from " + clientSocket.getInetAddress());

#         InputStream in = clientSocket.getInputStream();
#         OutputStream out = clientSocket.getOutputStream();

#         // greeting header
#         byte[] header = new byte[2];
#         in.read(header);
#         int version = header[0];
#         int nmethods = header[1];

#         // socks 5
#         if (version != SOCKS_VERSION) {
#             throw new IOException("Unsupported SOCKS version: " + version);
#         }
#         if (nmethods <= 0) {
#             throw new IOException("Invalid number of methods: " + nmethods);
#         }

#         // get available methods
#         byte[] methods = new byte[nmethods];
#         in.read(methods);

#         // accept only NO AUTH auth
#         boolean noAuth = false;
#         for (byte method : methods) {
#             if (method == 0) {
#                 noAuth = true;
#                 break;
#             }
#         }
#         if (!noAuth) {
#             throw new IOException("Unsupported authentication method");
#         }

#         // send welcome message
#         out.write(new byte[]{SOCKS_VERSION, 0});

#         // request
#         byte[] request = new byte[BUF_SIZE];
#         int len = in.read(request);
#         if (len < 0) {
#             throw new IOException("Invalid request");
#         }
#         int cmd = request[1];
#         int addressType = request[3];
#         String address;
#         int port;
#         switch (addressType) {
#             case 1: // IPv4
#                 address = InetAddress.getByAddress(request, 4, 4).getHostAddress();
#                 port = ((request[8] & 0xff) << 8) | (request[9] & 0xff);
#                 break;
#             case 3: // Domain name
#                 int domainLength = request[4];
#                 address = new String(request, 5, domainLength);
#                 port = ((request[5 + domainLength] & 0xff) << 8) | (request[6 + domainLength] & 0xff);
#                 break;
#             case 4: // IPv6
#                 address = InetAddress.getByAddress(request, 4, 16).getHostAddress();
#                 port = ((request[20] & 0xff) << 8) | (request[21] & 0xff);
#                 break;
#             default:
#                 throw new IOException("Unsupported address type: " + addressType);
#         }

#         // reply
#         try (Socket gatewaySocket = new Socket(gatewayAddress, gatewayPort)) {
#             OutputStream gatewayOut = gatewaySocket.getOutputStream();
#             InputStream gatewayIn = gatewaySocket.getInputStream();

#             byte[] reply = new byte[BUF_SIZE];
#             reply[0] = SOCKS_VERSION;
#             reply[1] = 0;
#             reply[2] = 0;
#             reply[3] = addressType;
#             switch (addressType) {
#                 case 1: // IPv4
#                     System.arraycopy(gatewaySocket.getLocalAddress().getAddress(), 0, reply, 4, 4);
#                     break;
#                 case 3: // Domain name
#                     reply[4] = (byte) address.length();
#                     System.arraycopy(address.getBytes(), 0, reply, 5, address.length());
#                     break;
#                 case 4: // IPv6
#                     System.arraycopy(gatewaySocket.getLocalAddress().getAddress(), 0, reply, 4, 16);
#                     break;
#             }
#             reply[4 + address.length()] = (byte) (port >> 8);
#             reply[5 + address.length()] = (byte) port;
#             out.write(reply, 0, 4 + address.length() + 2);

#             // establish data exchange
#             if (cmd == 1) { // CONNECT
#                 new Thread(() -> {
#                     try {
#                         byte[] buf = new byte[BUF_SIZE];
#                         int len1;
#                         while ((len1 = in.read(buf)) > 0) {
#                             gatewayOut.write(buf, 0, len1);
#                         }
#                     } catch (IOException e) {
#                         e.printStackTrace();
#                     }
#                 }).start();

#                 byte[] buf = new byte[BUF_SIZE];
#                 int len1;
#                 while ((len1 = gatewayIn.read(buf)) > 0) {
#                     out.write(buf, 0, len1);
#                 }
#             }
#         } catch (IOException e) {
#             e.printStackTrace();
#             byte[] reply = new byte[]{SOCKS_VERSION, 5, 0, addressType, 0, 0, 0, 0, 0, 0};
#             out.write(reply);
#         }

#         clientSocket.close();
#     }

#     public static void main(String[] args) throws IOException {
#         SocksProxyServer server = new SocksProxyServer(InetAddress.getByName("*************"), 22);
#         server.start(10810);
#     }
# }
