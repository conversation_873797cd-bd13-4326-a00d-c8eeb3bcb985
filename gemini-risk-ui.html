<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>风险报告</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
            margin: 0;
            padding: 20px;
            background-color: #f0f4f8; /* Light blue-gray background */
            color: #333;
            font-size: 14px;
        }
        .report-container {
            max-width: 800px;
            margin: auto;
            background: linear-gradient(to bottom, #e6f0ff 0%, #ffffff 30%); /* Gradient background */
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            overflow: hidden; /* Ensures inner elements respect border radius */
        }
        .header {
            display: flex;
            justify-content: space-between;
            padding: 15px 25px;
            font-size: 12px;
            color: #667;
            border-bottom: 1px solid #e0e0e0;
            background-color: rgba(255, 255, 255, 0.5); /* Slight white transparency */
        }
        .title-section {
            position: relative;
            padding: 30px 25px 20px 25px;
            text-align: left;
        }
        .main-title {
            font-size: 48px;
            font-weight: bold;
            color: #2a7fff; /* Bright blue */
            margin: 0;
            line-height: 1.2;
            letter-spacing: 2px;
        }
        .subtitle {
            font-size: 16px;
            color: #2a7fff;
            margin-top: 5px;
            letter-spacing: 1px;
            text-transform: uppercase;
        }
        .badge {
            position: absolute;
            top: 20px;
            right: 30px;
            width: 100px;
            height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .badge-background {
            position: absolute;
            width: 100%;
            height: 100%;
            background-color: #ff5c5c; /* Red color */
            border-radius: 50%;
            clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%); /* Star/Seal shape */
            transform: rotate(-10deg); /* Slight rotation */
        }
        .badge-text {
            position: relative;
            background-color: #ff5c5c;
            color: white;
            padding: 5px 15px;
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            transform: rotate(-15deg);
             /* Simple ribbon ends approximation */
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .info-section {
            padding: 0 25px 25px 25px;
        }
        .info-tab {
            display: inline-block;
            background-color: #2a7fff;
            color: white;
            padding: 8px 20px;
            border-radius: 8px 8px 0 0;
            font-size: 16px;
            font-weight: bold;
            position: relative;
            top: 1px; /* Overlap the border */
            margin-left: -10px; /* Slight offset */
            clip-path: polygon(0% 0%, 90% 0, 100% 100%, 0% 100%); /* Angled right edge */
            padding-right: 25px; /* Adjust padding for clip-path */
        }
        .info-card {
            background-color: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: 0 8px 8px 8px; /* Match tab */
            padding: 25px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        .user-info {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 25px;
            border-bottom: 1px dashed #e0e0e0; /* Dashed line separator */
        }
        .avatar {
            width: 50px;
            height: 50px;
            background-color: #d6eaff; /* Light blue avatar background */
            border-radius: 50%;
            margin-right: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            /* Basic placeholder icon */
            background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%232a7fff"><path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>');
            background-size: 60%;
            background-repeat: no-repeat;
            background-position: center;
        }
        .user-details {
            flex-grow: 1;
        }
        .user-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        .user-contact {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px; /* Spacing between items */
            font-size: 14px;
            color: #555;
        }
        .user-contact span {
            display: inline-flex;
            align-items: center;
            gap: 5px; /* Space between icon and text */
        }
        .icon {
            display: inline-block;
            width: 16px;
            height: 16px;
            /* Basic placeholder icons using background color */
            background-color: #ccc;
            margin-right: 5px;
            vertical-align: middle;
        }
        .icon-phone::before { content: '📞'; font-size: 14px; }
        .icon-cube::before { content: '📦'; font-size: 14px; }
        .icon-up::before { content: '⬆️'; font-size: 14px; }

        .details-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr; /* Three columns */
            gap: 20px;
            align-items: start;
        }
        .detail-item {
            font-size: 14px;
        }
        .detail-value {
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
            font-family: monospace; /* For better spacing of masked chars */
        }
        .detail-label {
            font-size: 12px;
            color: #777;
        }

    </style>
</head>
<body>

<div class="report-container">
    <div class="header">
        <span>报告编号: 74e854ssdad5584511asd55844</span>
        <span>报告生成时间: 2025-02-27 11:24:54</span>
    </div>

    <div class="title-section">
        <h1 class="main-title">风险报告</h1>
        <p class="subtitle">RISK REPORT</p>
        <div class="badge">
            <div class="badge-background"></div>
            <div class="badge-text">建议拒绝</div>
        </div>
    </div>

    <div class="info-section">
        <div class="info-tab">基础信息</div>
        <div class="info-card">
            <div class="user-info">
                <div class="avatar"></div>
                <div class="user-details">
                    <div class="user-name">吴**</div>
                    <div class="user-contact">
                        <span><span class="icon-phone"></span>150** **** **32</span>
                        <span>|</span>
                        <span><span class="icon-cube"></span>完整流程测试</span>
                        <span>|</span>
                        <span><span class="icon-up"></span>V1</span>
                    </div>
                </div>
            </div>

            <div class="details-grid">
                <div class="detail-item">
                    <div class="detail-value">6230**** **** **343</div>
                    <div class="detail-label">申请人银行卡号</div>
                </div>
                 <div class="detail-item">
                    <div class="detail-value">V1</div>
                    <div class="detail-label">业务产品版本</div>
                </div>
                 <div class="detail-item">
                    <div class="detail-value">V1</div>
                    <div class="detail-label">业务产品版本</div>
                </div>
                 <div class="detail-item">
                    <div class="detail-value">42052* **** **** ** 49</div>
                    <div class="detail-label">申请人证件号码</div>
                </div>
                 <div class="detail-item">
                    <div class="detail-value">完整流程测试</div>
                    <div class="detail-label">业务产品名称</div>
                </div>
                 <div class="detail-item">
                    <div class="detail-value">完整流程测试</div>
                    <div class="detail-label">业务产品名称</div>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>