presenter


内部数据源配置演示参数:
**************************************************************************************************
smartdecision
root@2024
crd_trade_log
执行逻辑
select count(1) as black_num from crd_trade_log where APP_NAME=? and APP_IDCARD=?

外部数据源演示配置
curl -X POST \
     -H "Content-Type: application/json" \
     -d '{"phone":"***********","name":"<PERSON><PERSON><PERSON>","id_number":"123456789012345678","bank_card":"****************"}' \
     http://47.98.118.143:18886/test_api



{"phone":"***********","name":"赵宜均","id_number":"210102197909279429","bank_card":"6222028529025435087"}     


{"code":"000","data":{"risk_level":"medium"},"msg":"处理成功"}
{"code":"000","data":{"risk_level":"low"},"msg":"处理成功"}

{"chargeCode":"0","code":"SUCCESS","data":{"abilityAttribute":"3.1","stabilityAttribute":"3.4","code":"00","pastAttribute":"2.7"},"errorMsg":"成功","status":0,"transNo":"2bf44807f4ba48f9a859c24264aa7cad"}


浙数银行卡涉网行为风险响应数据：{"chargeCode":"0","code":"1","data":{"53808d75dee07211ec2a6c38a5c3c138":{"gambling":{"hit":0},"pyramid":{"hit":0}}},"errorMsg":"","status":0,"transNo":"457d6469c610a91cfdc744ce56127e1d"}

{"code":"100","data":{"content":"FXUn45bZZ3K2Q5iB84UhDLhijfey0IlRVF9VkCnvST7O0CKhVr/1ZWhiiKdyFbStVe9hPn3cNHgIfXUXHPHiGs7lxZssz/iCb63wM729B0Y=","secretKey":"tIEe6ZlmetIqRzItuokb1VdIjq09VhAPnVRwfeXLpdk+QWW6pWpo76Yz0gUGap/le0AR/na3W5E5WBMvXVDdIRF5UC1HpuOzrGByIDWlxBlc0B5T5F3jWuI5qso/OlBhkW97CUZZtiniy9jswfkbxgI7AvMfTQL28oYNX4nG0o4="},"message":"操作失败"}



- 身份认证风险
  - 银行卡四要素验证
  - 公安不良记录

- 司法风险
  - 刑事案件
  - 民商事案件
  - 执行记录
  - 失信记录

- 多头借贷风险
  - 短期多头申请
  - 机构申请集中度
  - 夜间申请行为

- 交易行为风险
  - 套现风险
  - 博彩风险
  - 异常交易
  - 高危商户

- 还款能力风险
  - 社保缴纳
  - 消费能力
  - 履约能力

- 逾期风险
  - 历史逾期
  - 首逾表现

- 反欺诈规则
- 评分模型规则

在以上分类中，在筛选出来一票否决的强规则之后，在对不是一票否决的规则进行打分，根据打分的结果在去跑类型为中等的规则以及弱规则，最后在去跑评分类的规则，最终得出决策结果
ps 对目前的规则还需根据对应的风险类型大类进行编号，方便后续的规则调整以及规则的追踪
还需要考虑加入 黑白名单的规则



身份认证风险
  - 银行卡四要素验证
  - 公安不良记录
  - 团伙欺诈
  - 黑灰产等级

司法风险
  - 刑事案件
  - 民商事案件
  - 执行记录
  - 失信记录

 多头借贷风险
  - 短期多头申请
  - 机构申请集中度
  - 夜间申请行为

 交易行为风险
  - 套现风险
  - 博彩风险
  - 异常交易
  - 高危商户

还款能力风险
  - 社保缴纳
  - 消费能力
  - 履约能力
    偿债压力

逾期风险
  - 历史逾期
  - 首逾表现


利率偏好



稳定性
  - 运营商在网时长，状态
  - 社保缴纳历史数据
  - 生活稳定性

反欺诈规则

评分模型规则


近两年命中法院被执行人  court_executed_recent 大于等于80
近两年命中法院失信人   court_dishonest_recent 大于等于80
为法院失信被执行人且存在未履行的失信案件 dishonest_active 大于等于70
为法院失信被执行人且存在未履行的失信案件但已下架 dishonest_removed 大于等于100
为法院被执行人且未全部结案但已下架且五年前立案 executed_removed_5y_plus 大于等于100
为法院被执行人且未全部结案 executed_active 大于等于80


12个月内出现过15天以上的逾期 overdue_15d_12m 等于1
6个月内存在M2加的逾期  overdue_6d_36m 等于1
24个月内出现过贷款首期即逾期且逾期时间超过7天 first_overdue_7d_24m 等于1


浙数数据	netrisk	浙数银行卡涉网行为风险	card_fraud_risk	银行卡涉诈骗命中风险	"是否命中：
1 是，0 否；
当命中时才会返回下面的参数"	card_fraud_risk=1 and (card_fraud_last_reg_time＞=3 or card_fraud_last_login_time＞=3 and card_fraud_website_count＞=2 and card_fraud_amount＞=4)
百行征信	bhaf011010	百行反欺诈规则报告	anti_fraud_risk_level	百行反欺诈风险等级	"返回值为""high""
""medium""， ，""low""中
的任意一种。
未击中任何规则，返
回""""。其他返回字段参考 api 文档"	anti_fraud_risk_level=high
百融云创	bryc0008	百融风险哨兵指数	application_probe	申请探查	取值范围0-100，分数越高违约概率越高。返回为空：代表当前客户未匹配上该模型依赖的数据产品。	application_probe>=80
百融云创	bryc0008	百融风险哨兵指数	consumption_behavior_prediction	消费行为预测	取值范围0-100，分数越高违约概率越高。返回为空：代表当前客户未匹配上该模型依赖的数据产品。	consumption_behavior_prediction>=80
百融云创	bryc0008	百融风险哨兵指数	fulfillment_willingness	履约意愿	取值范围0-100，分数越高违约概率越高。返回为空：代表当前客户未匹配上该模型依赖的数据产品。	fulfillment_willingness>=80
百融云创	bryc0008	百融风险哨兵指数	trend_abnormality	趋势异常	取值范围0-100，分数越高违约概率越高。返回为空：代表当前客户未匹配上该模型依赖的数据产品。	trend_abnormality>=80
百融云创	bryc0008	百融风险哨兵指数	risk_sentinel	风险哨兵	取值范围0-100，分数越高违约概率越高。返回为空：代表当前客户未匹配上该模型依赖的数据产品。	risk_sentinel>=80
百融云创	bryc0008	百融风险哨兵指数	associated_abnormality	关联异常	取值范围为0-100分，分数越高代表关联越异常，违约概率越大。	associated_abnormality>=80



百融反欺诈规则特殊名单验证AND法院被执行人限高版	court_high_limit_removed	为法院限高被执行人已下架	court_high_limit_removed>=90 得分 50 权重 1

百融反欺诈规则AND偿债压力指数AND申请信息评估信用卡AND法院被执行人高级版AND借贷意向验证汽车金融AND反欺诈风险识别汽车金融	exposure_hit 命中曝光台	exposure_hit>=70	得分 50 权重 1

百融反欺诈规则偿债压力申请信息评估法院被执行人高级版借贷意向验证汽车风险识别汽车金融	dishonest_removed	为法院失信被执行人且存在未履行的失信案件但已下架 	dishonest_removed>=100 得分 50 权重 1
百融反欺诈规则偿债压力申请信息评估法院被执行人高级版借贷意向验证汽车风险识别汽车金融 	executed_removed_5y_plus	为法院被执行人且未全部结案但已下架且五年前立案	executed_removed_5y_plus>=100 得分 50 权重 1
百融反欺诈规则偿债压力申请信息评估法院被执行人高级版借贷意向验证汽车风险识别汽车金融	executed_active	为法院被执行人且未全部结案		executed_active>=80 得分 50 权重 1
百融反欺诈规则偿债压力申请信息评估法院被执行人高级版借贷意向验证汽车风险识别汽车金融	exposure_removed	命中曝光台已下架	百融命中规则，权重值，	exposure_removed>=100 得分 50 权重 1
百融反欺诈规则偿债压力申请信息评估法院被执行人高级版借贷意向验证汽车风险识别汽车金融	dishonest_fulfilled	为法院失信被执行人但所有案件均已履行		dishonest_fulfilled>=100 得分 50 权重 1
百融反欺诈规则偿债压力申请信息评估法院被执行人高级版借贷意向验证汽车风险识别汽车金融	executed_removed_5y	为法院被执行人且未全部结案但已下架且五年内立案		executed_removed_5y>=80 得分 50 权重 1
百融反欺诈规则偿债压力申请信息评估法院被执行人高级版借贷意向验证汽车风险识别汽车金融	executed_closed	曾为法院被执行人但已全部结案		executed_closed>=100 得分 50 权重 1



curl -X POST 'http://127.0.0.1:1786/api/risk-insight/accessToken' \
-H "Content-Type: application/json" \
-d '{
    "appKey": "APP_xPFaW7FqlBPrTGs4vxqG",
    "appSecret": "-73-dIQOevlUiM2uYH_xWtD9Aa3g5QdINWTwvRngMJoFVrmFmPi_OPT0BbUHtdD8"
}'


我想开发一个{类似小宇宙的播客app}，现在需要输出高保真的原型图，请通过以下方式帮我完成所有界面的原型设计，并确保这些原型界面可以直接用于开发：

1、用户体验分析：先分析这个 App 的主要功能和用户需求，确定核心交互逻辑。

2、产品界面规划：作为产品经理，定义关键界面，确保信息架构合理。

3、高保真 UI 设计：作为 UI 设计师，设计贴近真实 iOS/Android 设计规范的界面，使用现代化的 UI 元素，使其具有良好的视觉体验。

4、HTML 原型实现：使用 HTML + Tailwind CSS（或 Bootstrap）生成所有原型界面，并使用 FontAwesome（或其他开源 UI 组件）让界面更加精美、接近真实的 App 设计。

拆分代码文件，保持结构清晰：

5、每个界面应作为独立的 HTML 文件存放，例如 home.html、profile.html、settings.html 等。

- index.html 作为主入口，不直接写入所有界面的 HTML 代码，而是使用 iframe 的方式嵌入这些 HTML 片段，并将所有页面直接平铺展示在 index 页面中，而不是跳转链接。

- 真实感增强：

- 界面尺寸应模拟 iPhone 15 Pro，并让界面圆角化，使其更像真实的手机界面。

- 使用真实的 UI 图片，而非占位符图片（可从 Unsplash、Pexels、Apple 官方 UI 资源中选择）。

- 添加顶部状态栏（模拟 iOS 状态栏），并包含 App 导航栏（类似 iOS 底部 Tab Bar）。

请按照以上要求生成完整的 HTML 代码，并确保其可用于实际开发。



Create a single HTML file containing CSS and JavaScript to generate an animated weather card. The card should visually represent the following weather conditions with distinct animations: Wind: (e.g., moving clouds, swaying trees, or wind lines) Rain: (e.g., falling raindrops, puddles forming) Sun: (e.g., shining rays, bright background) Snow: (e.g., falling snowflakes, snow accumulating) Show all the weather card side by side The card should have a dark background. Provide all the HTML, CSS, and JavaScript code within this single file. The JavaScript should include a way to switch between the different weather conditions (e.g., a function or a set of buttons) to demonstrate the animations for each.


下面我会提供给你一个风控报告的设计 ui图，下面是需要你来帮我完成整个 ui 的实现，我会提供给你 ui 设计图 
后台返回的数据格式样列，我需要针对这个 ui 高度还原哦，使用 ssr 模式来实现这个项目，使用 recat 作为主要的开发框架



{
  "court_executed_recent": "unkonw",
  "execution_limited": "unkonw",
  "court_dishonest_active": "unkonw",
  "court_dishonest_recent": "unkonw",
  "debtor_count_to_date": "0",
  "execution_pro": "{execut1={casenum=（2018）鄂05执90号, dataType=最高法执行, sign=0, matchRatio=0.99, court=湖北省宜昌市中级人民法院, datatypeid=103, eventLevel=-2, money=9423838, signalDesc=主体被执行金额较大，且发生在三年以前，但已下架, name=杜波玲, signalRating=3, time=2018年01月19日, statute=0}}",
  "court_high_limit_active": "unkonw",
  "entry_list": "{\"sentencingDate\":\"\",\"filingDate\":\"\",\"caseStatus\":\"立案\",\"sortTime\":1429027200000,\"clerkPhone\":\"\",\"body\":\"金融借款合同纠纷 中级法院 中...\",\"title\":\"杜波玲\",\"caseType\":\"\",\"ajlcId\":\"c2015eyichangzhongminsanchu83_t20150415\",\"defendant\":\"被告:朱燕,宜昌滨湖物贸有限公司,朝枝元,杜波玲,宜昌诚功融资担保有限责任公司\",\"ajlcStatus\":\"立案受理\",\"member\":\"\",\"actionObject\":\"\",\"judge\":\"\",\"trialProcedure\":\"\",\"plaintiff\":\"原告:中国建设银行股份有限公司宜昌西陵支行\",\"pname\":\"杜波玲\",\"dataType\":\"ajlc\",\"matchRatio\":0.99,\"court\":\"湖北省宜昌市中级人民法院\",\"caseNo\":\"（2015）鄂宜昌中民三初字第00083号\",\"chiefJudge\":\"\",\"trialLimitDate\":\"\",\"clerk\":\"\",\"caseCause\":\"金融借款合同纠纷\",\"sortTimeString\":\"2015年04月15日\",\"organizer\":\"\",\"effectiveDate\":\"\",\"status\":0}",
  "cdp_blacklist": "unkonw"
}