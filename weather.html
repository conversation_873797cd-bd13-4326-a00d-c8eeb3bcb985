<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Premium Weather App</title>
    <style>
        :root {
            --primary-bg: #0f0e17;
            --card-bg: linear-gradient(145deg, #1a1b2f, #121420);
            --card-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
            --text-primary: #fffffe;
            --text-secondary: #a7a9be;
            --accent-sun: #ff8906;
            --accent-rain: #3da9fc;
            --accent-wind: #e53170;
            --accent-snow: #94f1f1;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            background: var(--primary-bg);
            color: var(--text-primary);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            overflow-x: hidden;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            position: relative;
            z-index: 10;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(90deg, #fffffe, #a7a9be);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            margin-bottom: 0.5rem;
            letter-spacing: -0.5px;
        }

        .header p {
            color: var(--text-secondary);
            font-size: 1.1rem;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .weather-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            width: 100%;
            max-width: 1400px;
            margin: 0 auto;
        }

        .weather-card {
            position: relative;
            background: var(--card-bg);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: var(--card-shadow);
            overflow: hidden;
            transition: transform 0.5s cubic-bezier(0.23, 1, 0.32, 1), box-shadow 0.5s ease;
            z-index: 1;
            height: 400px;
            display: flex;
            flex-direction: column;
        }

        .weather-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.4);
        }

        .weather-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            z-index: -1;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .weather-title {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .weather-icon {
            font-size: 2.5rem;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(5px);
        }

        .weather-desc {
            color: var(--text-secondary);
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .weather-details {
            margin-top: auto;
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
        }

        .temperature {
            font-size: 3rem;
            font-weight: 300;
            line-height: 1;
        }

        .meta {
            text-align: right;
        }

        .meta-item {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        /* Wind Card Specific Styles */
        .wind-card {
            border-top: 3px solid var(--accent-wind);
        }

        .wind-card .weather-icon {
            color: var(--accent-wind);
        }

        .wind-particle {
            position: absolute;
            width: 2px;
            height: 20px;
            background: linear-gradient(to bottom, transparent, var(--accent-wind));
            opacity: 0.8;
            filter: blur(1px);
            animation: windFlow 8s linear infinite;
        }

        .wind-cloud {
            position: absolute;
            font-size: 3rem;
            opacity: 0.7;
            animation: windMove 15s linear infinite;
            z-index: -1;
        }

        @keyframes windFlow {
            0% {
                transform: translateX(-50px) translateY(0) rotate(20deg);
                opacity: 0;
            }
            20% {
                opacity: 0.8;
            }
            100% {
                transform: translateX(400px) translateY(50px) rotate(20deg);
                opacity: 0;
            }
        }

        @keyframes windMove {
            0% {
                transform: translateX(-100px) translateY(0);
            }
            100% {
                transform: translateX(400px) translateY(30px);
            }
        }

        /* Rain Card Specific Styles */
        .rain-card {
            border-top: 3px solid var(--accent-rain);
        }

        .rain-card .weather-icon {
            color: var(--accent-rain);
        }

        .raindrop {
            position: absolute;
            width: 1px;
            height: 15px;
            background: linear-gradient(to bottom, transparent, var(--accent-rain));
            border-radius: 0 0 5px 5px;
            filter: blur(0.5px);
            animation: rainFall linear infinite;
            z-index: -1;
        }

        .puddle {
            position: absolute;
            bottom: 20px;
            width: 40px;
            height: 5px;
            background: rgba(61, 169, 252, 0.2);
            border-radius: 50%;
            filter: blur(2px);
            animation: puddleRipple 3s infinite ease-in-out;
            z-index: -1;
        }

        @keyframes rainFall {
            0% {
                transform: translateY(-10px);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(400px);
                opacity: 0;
            }
        }

        @keyframes puddleRipple {
            0% {
                transform: scale(0.8);
                opacity: 0;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.3;
            }
            100% {
                transform: scale(0.8);
                opacity: 0;
            }
        }

        /* Sun Card Specific Styles */
        .sun-card {
            border-top: 3px solid var(--accent-sun);
        }

        .sun-card .weather-icon {
            color: var(--accent-sun);
        }

        .sun {
            position: absolute;
            width: 100px;
            height: 100px;
            background: radial-gradient(circle, var(--accent-sun), #ff5e00);
            border-radius: 50%;
            top: 50px;
            right: 50px;
            filter: blur(1px);
            animation: sunPulse 4s infinite alternate ease-in-out;
            z-index: -1;
        }

        .sun-ray {
            position: absolute;
            width: 120px;
            height: 2px;
            background: linear-gradient(to right, rgba(255, 137, 6, 0.6), transparent);
            top: 50%;
            left: 50%;
            transform-origin: left center;
            filter: blur(1px);
            animation: sunRotate 30s linear infinite;
            z-index: -1;
        }

        @keyframes sunPulse {
            0% {
                box-shadow: 0 0 30px rgba(255, 137, 6, 0.4);
            }
            100% {
                box-shadow: 0 0 80px rgba(255, 137, 6, 0.6);
            }
        }

        @keyframes sunRotate {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        /* Snow Card Specific Styles */
        .snow-card {
            border-top: 3px solid var(--accent-snow);
        }

        .snow-card .weather-icon {
            color: var(--accent-snow);
        }

        .snowflake {
            position: absolute;
            color: white;
            font-size: 1rem;
            opacity: 0.9;
            filter: blur(0.5px);
            animation: snowFall linear infinite;
            z-index: -1;
        }

        @keyframes snowFall {
            0% {
                transform: translateY(-20px) translateX(0) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(400px) translateX(30px) rotate(360deg);
                opacity: 0;
            }
        }

        /* Controls */
        .controls {
            margin-top: 3rem;
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            justify-content: center;
        }

        .control-btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 50px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
            backdrop-filter: blur(5px);
        }

        .control-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.1), transparent);
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .control-btn:hover::before {
            transform: translateX(0);
        }

        .wind-btn { color: var(--accent-wind); }
        .rain-btn { color: var(--accent-rain); }
        .sun-btn { color: var(--accent-sun); }
        .snow-btn { color: var(--accent-snow); }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .weather-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }

        /* Floating particles background */
        .particle {
            position: fixed;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.05);
            animation: float 15s infinite linear;
            z-index: -2;
        }

        @keyframes float {
            0% {
                transform: translateY(0) rotate(0deg);
            }
            100% {
                transform: translateY(-100vh) rotate(360deg);
            }
        }
    </style>
</head>
<body>
    <!-- Background particles -->
    <div id="particles"></div>

    <div class="header">
        <h1>Premium Weather Visualizations</h1>
        <p>Experience weather like never before with our cinematic animations and real-time effects</p>
    </div>

    <div class="weather-grid">
        <!-- Wind Card -->
        <div class="weather-card wind-card">
            <div class="card-header">
                <h2 class="weather-title">Wind Storm</h2>
                <div class="weather-icon">💨</div>
            </div>
            <p class="weather-desc">Strong winds at 25 km/h with gusts up to 40 km/h. Hold onto your hats!</p>
            <div class="weather-details">
                <div class="temperature">12°</div>
                <div class="meta">
                    <div class="meta-item">Humidity: 65%</div>
                    <div class="meta-item">Pressure: 1012 hPa</div>
                </div>
            </div>
            <div class="wind-cloud">☁️</div>
            <div class="wind-cloud" style="top: 100px; animation-delay: 3s;">☁️</div>
        </div>
        
        <!-- Rain Card -->
        <div class="weather-card rain-card">
            <div class="card-header">
                <h2 class="weather-title">Heavy Rain</h2>
                <div class="weather-icon">🌧️</div>
            </div>
            <p class="weather-desc">Expect heavy rainfall throughout the day. Don't forget your umbrella!</p>
            <div class="weather-details">
                <div class="temperature">16°</div>
                <div class="meta">
                    <div class="meta-item">Precipitation: 90%</div>
                    <div class="meta-item">Visibility: 2 km</div>
                </div>
            </div>
            <div class="puddle" style="left: 30px; animation-delay: 0.5s;"></div>
            <div class="puddle" style="left: 100px; animation-delay: 1s;"></div>
            <div class="puddle" style="left: 170px; animation-delay: 1.5s;"></div>
        </div>
        
        <!-- Sun Card -->
        <div class="weather-card sun-card">
            <div class="card-header">
                <h2 class="weather-title">Sunny Day</h2>
                <div class="weather-icon">☀️</div>
            </div>
            <p class="weather-desc">Clear skies with abundant sunshine. Perfect day for outdoor activities!</p>
            <div class="weather-details">
                <div class="temperature">28°</div>
                <div class="meta">
                    <div class="meta-item">UV Index: 8 (Very High)</div>
                    <div class="meta-item">Sunset: 8:12 PM</div>
                </div>
            </div>
            <div class="sun"></div>
        </div>
        
        <!-- Snow Card -->
        <div class="weather-card snow-card">
            <div class="card-header">
                <h2 class="weather-title">Snow Fall</h2>
                <div class="weather-icon">❄️</div>
            </div>
            <p class="weather-desc">Light snowfall accumulating 2-4 cm. Roads may be slippery.</p>
            <div class="weather-details">
                <div class="temperature">-3°</div>
                <div class="meta">
                    <div class="meta-item">Snow depth: 4 cm</div>
                    <div class="meta-item">Wind chill: -8°</div>
                </div>
            </div>
        </div>
    </div>

    <div class="controls">
        <button class="control-btn wind-btn" onclick="toggleAnimation('wind')">Wind Effects</button>
        <button class="control-btn rain-btn" onclick="toggleAnimation('rain')">Rain Effects</button>
        <button class="control-btn sun-btn" onclick="toggleAnimation('sun')">Sun Effects</button>
        <button class="control-btn snow-btn" onclick="toggleAnimation('snow')">Snow Effects</button>
    </div>

    <script>
        // Create weather particles
        function createParticles() {
            const windCard = document.querySelector('.wind-card');
            for (let i = 0; i < 20; i++) {
                const particle = document.createElement('div');
                particle.className = 'wind-particle';
                particle.style.left = `${Math.random() * 100}%`;
                particle.style.top = `${Math.random() * 100}%`;
                particle.style.animationDuration = `${5 + Math.random() * 10}s`;
                particle.style.animationDelay = `${Math.random() * 5}s`;
                windCard.appendChild(particle);
            }

            const rainCard = document.querySelector('.rain-card');
            for (let i = 0; i < 60; i++) {
                const raindrop = document.createElement('div');
                raindrop.className = 'raindrop';
                raindrop.style.left = `${Math.random() * 100}%`;
                raindrop.style.animationDuration = `${0.8 + Math.random() * 1.2}s`;
                raindrop.style.animationDelay = `${Math.random() * 2}s`;
                rainCard.appendChild(raindrop);
            }

            const sunCard = document.querySelector('.sun-card');
            for (let i = 0; i < 12; i++) {
                const ray = document.createElement('div');
                ray.className = 'sun-ray';
                ray.style.transform = `rotate(${i * 30}deg)`;
                ray.style.animationDuration = `${20 + Math.random() * 10}s`;
                sunCard.appendChild(ray);
            }

            const snowCard = document.querySelector('.snow-card');
            for (let i = 0; i < 30; i++) {
                const snowflake = document.createElement('div');
                snowflake.className = 'snowflake';
                snowflake.innerHTML = '❄️';
                snowflake.style.left = `${Math.random() * 100}%`;
                snowflake.style.animationDuration = `${5 + Math.random() * 10}s`;
                snowflake.style.animationDelay = `${Math.random() * 5}s`;
                snowflake.style.fontSize = `${0.8 + Math.random() * 1.2}rem`;
                snowCard.appendChild(snowflake);
            }
        }

        // Create background particles
        function createBackgroundParticles() {
            const container = document.getElementById('particles');
            const particleCount = Math.floor(window.innerWidth / 10);
            
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                
                const size = Math.random() * 5 + 1;
                particle.style.width = `${size}px`;
                particle.style.height = `${size}px`;
                
                particle.style.left = `${Math.random() * 100}vw`;
                particle.style.top = `${Math.random() * 100}vh`;
                
                particle.style.animationDuration = `${10 + Math.random() * 20}s`;
                particle.style.animationDelay = `${Math.random() * 10}s`;
                
                container.appendChild(particle);
            }
        }

        // Toggle animation function
        function toggleAnimation(type) {
            const card = document.querySelector(`.${type}-card`);
            const elements = card.querySelectorAll('div:not(.card-header):not(.weather-title):not(.weather-icon):not(.weather-desc):not(.weather-details)');
            
            elements.forEach(el => {
                if (el.style.animationPlayState === 'paused') {
                    el.style.animationPlayState = 'running';
                } else {
                    el.style.animationPlayState = 'paused';
                }
            });
        }

        // Initialize on load
        window.addEventListener('load', () => {
            createParticles();
            createBackgroundParticles();
            
            // Add subtle hover effect to cards
            const cards = document.querySelectorAll('.weather-card');
            cards.forEach(card => {
                card.addEventListener('mousemove', (e) => {
                    const rect = card.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;
                    
                    card.style.setProperty('--mouse-x', `${x}px`);
                    card.style.setProperty('--mouse-y', `${y}px`);
                });
            });
        });
    </script>
</body>
</html>
