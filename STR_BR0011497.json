{"code": "00", "RiskStrategy": {"strategy_version": "1.0", "product_type": "100099", "final_decision": "Reject", "strategy_id": "STR_BR0011497", "Rule": {"decision": "Reject", "weight": "100"}, "product_name": "预置_反欺诈规则-借贷意向验证3.0-通用客群", "platform": "web", "scene": "lend"}, "swift_number": "3034679_20250102155950_6418", "ApplyEvaluate": {"d7": {"id": {"orgnum": "1", "bank": {"orgnum": "1", "night_allnum": "1", "weekend_orgnum": "1", "weekend_allnum": "1", "orgnum_d": "1", "allnum_d": "1", "region_allnum": "1", "night_orgnum": "1", "national_orgnum": "1", "region_orgnum": "1", "national_allnum": "1", "allnum": "1"}, "nbank": {"else_cons_allnum": "1", "orgnum": "1", "sloan_allnum": "1", "top_orgnum": "1", "else_pdl_orgnum": "1", "trans_orgnum": "1", "night_allnum": "1", "cons_orgnum": "1", "else_pdl_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "weekend_orgnum": "1", "allnum_d": "1", "autofin_allnum": "1", "ins_orgnum": "1", "allnum": "1", "trans_allnum": "1", "else_cons_orgnum": "1", "else_rel_allnum": "1", "weekend_allnum": "1", "orgnum_d": "1", "else_rel_orgnum": "1", "night_orgnum": "1", "ins_allnum": "1", "top_allnum": "1", "autofin_orgnum": "1"}, "orgnum_d": "1", "allnum_d": "1", "allnum": "1"}, "cell": {"orgnum": "1", "bank": {"orgnum": "1", "night_allnum": "1", "weekend_orgnum": "1", "weekend_allnum": "1", "orgnum_d": "1", "allnum_d": "1", "region_allnum": "1", "night_orgnum": "1", "national_orgnum": "1", "region_orgnum": "1", "national_allnum": "1", "allnum": "1"}, "nbank": {"else_cons_allnum": "1", "orgnum": "1", "sloan_allnum": "1", "top_orgnum": "1", "else_pdl_orgnum": "1", "trans_orgnum": "1", "night_allnum": "1", "cons_orgnum": "1", "else_pdl_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "weekend_orgnum": "1", "allnum_d": "1", "autofin_allnum": "1", "ins_orgnum": "1", "allnum": "1", "trans_allnum": "1", "else_cons_orgnum": "1", "else_rel_allnum": "1", "weekend_allnum": "1", "orgnum_d": "1", "else_rel_orgnum": "1", "night_orgnum": "1", "ins_allnum": "1", "top_allnum": "1", "autofin_orgnum": "1"}, "orgnum_d": "1", "allnum_d": "1", "allnum": "1"}}, "m1": {"id": {"orgnum": "1", "bank": {"orgnum": "1", "night_allnum": "1", "weekend_orgnum": "1", "weekend_allnum": "1", "orgnum_d": "1", "allnum_d": "1", "region_allnum": "1", "night_orgnum": "1", "national_orgnum": "1", "region_orgnum": "1", "national_allnum": "1", "allnum": "1"}, "nbank": {"else_cons_allnum": "1", "orgnum": "1", "sloan_allnum": "1", "top_orgnum": "1", "else_pdl_orgnum": "1", "trans_orgnum": "1", "night_allnum": "1", "cons_orgnum": "1", "else_pdl_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "weekend_orgnum": "1", "allnum_d": "1", "autofin_allnum": "1", "ins_orgnum": "1", "allnum": "1", "trans_allnum": "1", "else_cons_orgnum": "1", "else_rel_allnum": "1", "weekend_allnum": "1", "orgnum_d": "1", "else_rel_orgnum": "1", "night_orgnum": "1", "ins_allnum": "1", "top_allnum": "1", "autofin_orgnum": "1"}, "orgnum_d": "1", "allnum_d": "1", "allnum": "1"}, "cell": {"orgnum": "1", "bank": {"orgnum": "1", "night_allnum": "1", "weekend_orgnum": "1", "weekend_allnum": "1", "orgnum_d": "1", "allnum_d": "1", "region_allnum": "1", "night_orgnum": "1", "national_orgnum": "1", "region_orgnum": "1", "national_allnum": "1", "allnum": "1"}, "nbank": {"else_cons_allnum": "1", "orgnum": "1", "sloan_allnum": "1", "top_orgnum": "1", "else_pdl_orgnum": "1", "trans_orgnum": "1", "night_allnum": "1", "cons_orgnum": "1", "else_pdl_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "weekend_orgnum": "1", "allnum_d": "1", "autofin_allnum": "1", "ins_orgnum": "1", "allnum": "1", "trans_allnum": "1", "else_cons_orgnum": "1", "else_rel_allnum": "1", "weekend_allnum": "1", "orgnum_d": "1", "else_rel_orgnum": "1", "night_orgnum": "1", "ins_allnum": "1", "top_allnum": "1", "autofin_orgnum": "1"}, "orgnum_d": "1", "allnum_d": "1", "allnum": "1"}}, "m3": {"id": {"tot_mons": "1", "orgnum": "1", "bank": {"orgnum": "1", "weekend_orgnum": "1", "allnum_d": "1", "region_allnum": "1", "national_orgnum": "1", "allnum": "1", "tot_mons": "1", "night_allnum": "1", "weekend_allnum": "1", "max_monnum": "1", "orgnum_d": "1", "night_orgnum": "1", "region_orgnum": "1", "national_allnum": "1", "avg_monnum": "1.00", "min_monnum": "1"}, "max_monnum": "1", "nbank": {"else_cons_allnum": "1", "orgnum": "1", "sloan_allnum": "1", "top_orgnum": "1", "else_pdl_orgnum": "1", "trans_orgnum": "1", "night_allnum": "1", "cons_orgnum": "1", "else_pdl_allnum": "1", "sloan_orgnum": "1", "min_monnum": "1", "cons_allnum": "1", "weekend_orgnum": "1", "allnum_d": "1", "autofin_allnum": "1", "ins_orgnum": "1", "allnum": "1", "trans_allnum": "1", "else_cons_orgnum": "1", "else_rel_allnum": "1", "tot_mons": "1", "weekend_allnum": "1", "max_monnum": "1", "orgnum_d": "1", "else_rel_orgnum": "1", "night_orgnum": "1", "ins_allnum": "1", "top_allnum": "1", "autofin_orgnum": "1", "avg_monnum": "1.00"}, "orgnum_d": "1", "allnum_d": "1", "allnum": "1", "avg_monnum": "1.00", "min_monnum": "1"}, "cell": {"tot_mons": "1", "orgnum": "1", "bank": {"orgnum": "1", "weekend_orgnum": "1", "allnum_d": "1", "region_allnum": "1", "national_orgnum": "1", "allnum": "1", "tot_mons": "1", "night_allnum": "1", "weekend_allnum": "1", "max_monnum": "1", "orgnum_d": "1", "night_orgnum": "1", "region_orgnum": "1", "national_allnum": "1", "avg_monnum": "1.00", "min_monnum": "1"}, "max_monnum": "1", "nbank": {"else_cons_allnum": "1", "orgnum": "1", "sloan_allnum": "1", "top_orgnum": "1", "else_pdl_orgnum": "1", "trans_orgnum": "1", "night_allnum": "1", "cons_orgnum": "1", "else_pdl_allnum": "1", "sloan_orgnum": "1", "min_monnum": "1", "cons_allnum": "1", "weekend_orgnum": "1", "allnum_d": "1", "autofin_allnum": "1", "ins_orgnum": "1", "allnum": "1", "trans_allnum": "1", "else_cons_orgnum": "1", "else_rel_allnum": "1", "tot_mons": "1", "weekend_allnum": "1", "max_monnum": "1", "orgnum_d": "1", "else_rel_orgnum": "1", "night_orgnum": "1", "ins_allnum": "1", "top_allnum": "1", "autofin_orgnum": "1", "avg_monnum": "1.00"}, "orgnum_d": "1", "allnum_d": "1", "allnum": "1", "avg_monnum": "1.00", "min_monnum": "1"}}, "d15": {"id": {"orgnum": "1", "bank": {"orgnum": "1", "night_allnum": "1", "weekend_orgnum": "1", "weekend_allnum": "1", "orgnum_d": "1", "allnum_d": "1", "region_allnum": "1", "night_orgnum": "1", "national_orgnum": "1", "region_orgnum": "1", "national_allnum": "1", "allnum": "1"}, "nbank": {"else_cons_allnum": "1", "orgnum": "1", "sloan_allnum": "1", "top_orgnum": "1", "else_pdl_orgnum": "1", "trans_orgnum": "1", "night_allnum": "1", "cons_orgnum": "1", "else_pdl_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "weekend_orgnum": "1", "allnum_d": "1", "autofin_allnum": "1", "ins_orgnum": "1", "allnum": "1", "trans_allnum": "1", "else_cons_orgnum": "1", "else_rel_allnum": "1", "weekend_allnum": "1", "orgnum_d": "1", "else_rel_orgnum": "1", "night_orgnum": "1", "ins_allnum": "1", "top_allnum": "1", "autofin_orgnum": "1"}, "orgnum_d": "1", "allnum_d": "1", "allnum": "1"}, "cell": {"orgnum": "1", "bank": {"orgnum": "1", "night_allnum": "1", "weekend_orgnum": "1", "weekend_allnum": "1", "orgnum_d": "1", "allnum_d": "1", "region_allnum": "1", "night_orgnum": "1", "national_orgnum": "1", "region_orgnum": "1", "national_allnum": "1", "allnum": "1"}, "nbank": {"else_cons_allnum": "1", "orgnum": "1", "sloan_allnum": "1", "top_orgnum": "1", "else_pdl_orgnum": "1", "trans_orgnum": "1", "night_allnum": "1", "cons_orgnum": "1", "else_pdl_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "weekend_orgnum": "1", "allnum_d": "1", "autofin_allnum": "1", "ins_orgnum": "1", "allnum": "1", "trans_allnum": "1", "else_cons_orgnum": "1", "else_rel_allnum": "1", "weekend_allnum": "1", "orgnum_d": "1", "else_rel_orgnum": "1", "night_orgnum": "1", "ins_allnum": "1", "top_allnum": "1", "autofin_orgnum": "1"}, "orgnum_d": "1", "allnum_d": "1", "allnum": "1"}}, "m6": {"id": {"tot_mons": "1", "orgnum": "1", "bank": {"orgnum": "1", "weekend_orgnum": "1", "allnum_d": "1", "region_allnum": "1", "national_orgnum": "1", "allnum": "1", "tot_mons": "1", "night_allnum": "1", "weekend_allnum": "1", "max_monnum": "1", "orgnum_d": "1", "night_orgnum": "1", "region_orgnum": "1", "national_allnum": "1", "avg_monnum": "1.00", "min_monnum": "1"}, "max_monnum": "1", "nbank": {"else_cons_allnum": "1", "orgnum": "1", "sloan_allnum": "1", "top_orgnum": "1", "else_pdl_orgnum": "1", "trans_orgnum": "1", "night_allnum": "1", "cons_orgnum": "1", "else_pdl_allnum": "1", "sloan_orgnum": "1", "min_monnum": "1", "cons_allnum": "1", "weekend_orgnum": "1", "allnum_d": "1", "autofin_allnum": "1", "ins_orgnum": "1", "allnum": "1", "trans_allnum": "1", "else_cons_orgnum": "1", "else_rel_allnum": "1", "tot_mons": "1", "weekend_allnum": "1", "max_monnum": "1", "orgnum_d": "1", "else_rel_orgnum": "1", "night_orgnum": "1", "ins_allnum": "1", "top_allnum": "1", "autofin_orgnum": "1", "avg_monnum": "1.00"}, "orgnum_d": "1", "allnum_d": "1", "allnum": "1", "avg_monnum": "1.00", "min_monnum": "1"}, "cell": {"tot_mons": "1", "orgnum": "1", "bank": {"orgnum": "1", "weekend_orgnum": "1", "allnum_d": "1", "region_allnum": "1", "national_orgnum": "1", "allnum": "1", "tot_mons": "1", "night_allnum": "1", "weekend_allnum": "1", "max_monnum": "1", "orgnum_d": "1", "night_orgnum": "1", "region_orgnum": "1", "national_allnum": "1", "avg_monnum": "1.00", "min_monnum": "1"}, "max_monnum": "1", "nbank": {"else_cons_allnum": "1", "orgnum": "1", "sloan_allnum": "1", "top_orgnum": "1", "else_pdl_orgnum": "1", "trans_orgnum": "1", "night_allnum": "1", "cons_orgnum": "1", "else_pdl_allnum": "1", "sloan_orgnum": "1", "min_monnum": "1", "cons_allnum": "1", "weekend_orgnum": "1", "allnum_d": "1", "autofin_allnum": "1", "ins_orgnum": "1", "allnum": "1", "trans_allnum": "1", "else_cons_orgnum": "1", "else_rel_allnum": "1", "tot_mons": "1", "weekend_allnum": "1", "max_monnum": "1", "orgnum_d": "1", "else_rel_orgnum": "1", "night_orgnum": "1", "ins_allnum": "1", "top_allnum": "1", "autofin_orgnum": "1", "avg_monnum": "1.00"}, "orgnum_d": "1", "allnum_d": "1", "allnum": "1", "avg_monnum": "1.00", "min_monnum": "1"}}, "d3": {"id": {"orgnum": "1", "bank": {"orgnum": "1", "night_allnum": "1", "orgnum_d": "1", "allnum_d": "1", "region_allnum": "1", "night_orgnum": "1", "national_orgnum": "1", "region_orgnum": "1", "national_allnum": "1", "allnum": "1"}, "nbank": {"cons_allnum": "1", "else_cons_allnum": "1", "orgnum": "1", "allnum_d": "1", "sloan_allnum": "1", "top_orgnum": "1", "autofin_allnum": "1", "ins_orgnum": "1", "allnum": "1", "trans_allnum": "1", "else_cons_orgnum": "1", "else_pdl_orgnum": "1", "else_rel_allnum": "1", "trans_orgnum": "1", "night_allnum": "1", "orgnum_d": "1", "cons_orgnum": "1", "else_rel_orgnum": "1", "else_pdl_allnum": "1", "night_orgnum": "1", "ins_allnum": "1", "top_allnum": "1", "sloan_orgnum": "1", "autofin_orgnum": "1"}, "orgnum_d": "1", "allnum_d": "1", "allnum": "1"}, "cell": {"orgnum": "1", "bank": {"orgnum": "1", "night_allnum": "1", "orgnum_d": "1", "allnum_d": "1", "region_allnum": "1", "night_orgnum": "1", "national_orgnum": "1", "region_orgnum": "1", "national_allnum": "1", "allnum": "1"}, "nbank": {"cons_allnum": "1", "else_cons_allnum": "1", "orgnum": "1", "allnum_d": "1", "sloan_allnum": "1", "top_orgnum": "1", "autofin_allnum": "1", "ins_orgnum": "1", "allnum": "1", "trans_allnum": "1", "else_cons_orgnum": "1", "else_pdl_orgnum": "1", "else_rel_allnum": "1", "trans_orgnum": "1", "night_allnum": "1", "orgnum_d": "1", "cons_orgnum": "1", "else_rel_orgnum": "1", "else_pdl_allnum": "1", "night_orgnum": "1", "ins_allnum": "1", "top_allnum": "1", "sloan_orgnum": "1", "autofin_orgnum": "1"}, "orgnum_d": "1", "allnum_d": "1", "allnum": "1"}}, "m12": {"id": {"tot_mons": "1", "orgnum": "1", "bank": {"orgnum": "1", "weekend_orgnum": "1", "allnum_d": "1", "region_allnum": "1", "national_orgnum": "1", "allnum": "1", "tot_mons": "1", "night_allnum": "1", "weekend_allnum": "1", "max_monnum": "1", "orgnum_d": "1", "night_orgnum": "1", "region_orgnum": "1", "national_allnum": "1", "avg_monnum": "1.00", "min_monnum": "1"}, "max_monnum": "1", "nbank": {"else_cons_allnum": "1", "orgnum": "1", "sloan_allnum": "1", "top_orgnum": "1", "else_pdl_orgnum": "1", "trans_orgnum": "1", "night_allnum": "1", "cons_orgnum": "1", "else_pdl_allnum": "1", "sloan_orgnum": "1", "min_monnum": "1", "cons_allnum": "1", "weekend_orgnum": "1", "allnum_d": "1", "autofin_allnum": "1", "ins_orgnum": "1", "allnum": "1", "trans_allnum": "1", "else_cons_orgnum": "1", "else_rel_allnum": "1", "tot_mons": "1", "weekend_allnum": "1", "max_monnum": "1", "orgnum_d": "1", "else_rel_orgnum": "1", "night_orgnum": "1", "ins_allnum": "1", "top_allnum": "1", "autofin_orgnum": "1", "avg_monnum": "1.00"}, "orgnum_d": "1", "allnum_d": "1", "allnum": "1", "avg_monnum": "1.00", "min_monnum": "1"}, "cell": {"tot_mons": "1", "orgnum": "1", "bank": {"orgnum": "1", "weekend_orgnum": "1", "allnum_d": "1", "region_allnum": "1", "national_orgnum": "1", "allnum": "1", "tot_mons": "1", "night_allnum": "1", "weekend_allnum": "1", "max_monnum": "1", "orgnum_d": "1", "night_orgnum": "1", "region_orgnum": "1", "national_allnum": "1", "avg_monnum": "1.00", "min_monnum": "1"}, "max_monnum": "1", "nbank": {"else_cons_allnum": "1", "orgnum": "1", "sloan_allnum": "1", "top_orgnum": "1", "else_pdl_orgnum": "1", "trans_orgnum": "1", "night_allnum": "1", "cons_orgnum": "1", "else_pdl_allnum": "1", "sloan_orgnum": "1", "min_monnum": "1", "cons_allnum": "1", "weekend_orgnum": "1", "allnum_d": "1", "autofin_allnum": "1", "ins_orgnum": "1", "allnum": "1", "trans_allnum": "1", "else_cons_orgnum": "1", "else_rel_allnum": "1", "tot_mons": "1", "weekend_allnum": "1", "max_monnum": "1", "orgnum_d": "1", "else_rel_orgnum": "1", "night_orgnum": "1", "ins_allnum": "1", "top_allnum": "1", "autofin_orgnum": "1", "avg_monnum": "1.00"}, "orgnum_d": "1", "allnum_d": "1", "allnum": "1", "avg_monnum": "1.00", "min_monnum": "1"}}}, "Rule": {"result": {"final_weight": "100", "final_decision": "Reject"}, "hit_rules": {"ruleapplyevaluate_usual": {"AEU021": {"weight": "80", "name_rule": "长期在非银机构申请次数极多"}, "AEU022": {"weight": "60", "name_rule": "长期在非银机构申请次数较多"}, "AEU020": {"weight": "60", "name_rule": "长期申请次数较多"}, "AEU018": {"weight": "60", "name_rule": "近期平均每月申请次数较多"}, "AEU019": {"weight": "80", "name_rule": "长期申请次数极多"}, "AEU016": {"weight": "60", "name_rule": "近期在非银机构最小月申请次数较多"}, "AEU017": {"weight": "80", "name_rule": "近期平均每月申请次数极多"}, "AEU014": {"weight": "60", "name_rule": "近期最小月申请次数较多"}, "AEU015": {"weight": "80", "name_rule": "近期在非银机构最小月申请次数极多"}, "AEU012": {"weight": "60", "name_rule": "近期在非银机构最大月申请次数较多"}, "AEU034": {"weight": "60", "name_rule": "长期平均每月申请次数较多"}, "AEU013": {"weight": "80", "name_rule": "近期最小月申请次数极多"}, "AEU010": {"weight": "60", "name_rule": "近期最大月申请次数较多"}, "AEU032": {"weight": "60", "name_rule": "长期在非银机构最小月申请次数较多"}, "AEU011": {"weight": "80", "name_rule": "近期在非银机构最大月申请次数极多"}, "AEU033": {"weight": "80", "name_rule": "长期平均每月申请次数极多"}, "AEU030": {"weight": "60", "name_rule": "长期最小月申请次数较多"}, "AEU031": {"weight": "80", "name_rule": "长期在非银机构最小月申请次数极多"}, "AEU009": {"weight": "80", "name_rule": "近期最大月申请次数极多"}, "AEU007": {"weight": "80", "name_rule": "近期在非银机构申请机构数极多"}, "AEU029": {"weight": "80", "name_rule": "长期最小月申请次数极多"}, "AEU008": {"weight": "60", "name_rule": "近期在非银机构申请机构数较多"}, "AEU005": {"weight": "80", "name_rule": "近期申请机构数极多"}, "AEU027": {"weight": "80", "name_rule": "长期最大月申请次数极多"}, "AEU006": {"weight": "60", "name_rule": "近期申请机构数较多"}, "AEU028": {"weight": "60", "name_rule": "长期最大月申请次数较多"}, "AEU003": {"weight": "80", "name_rule": "近期在非银机构申请次数极多"}, "AEU025": {"weight": "80", "name_rule": "长期在非银机构申请机构数极多"}, "AEU004": {"weight": "60", "name_rule": "近期在非银机构申请次数较多"}, "AEU026": {"weight": "60", "name_rule": "长期在非银机构申请机构数较多"}, "AEU001": {"weight": "80", "name_rule": "近期申请次数极多"}, "AEU023": {"weight": "80", "name_rule": "长期申请机构数极多"}, "AEU002": {"weight": "60", "name_rule": "近期申请次数较多"}, "AEU024": {"weight": "60", "name_rule": "长期申请机构数较多"}}}}, "Flag": {"riskstrategy": "1", "applyevaluate": "1", "ruleapplyevaluate_usual": "1"}}