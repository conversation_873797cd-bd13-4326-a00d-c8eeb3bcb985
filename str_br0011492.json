{"InfoRelation": {"cell_is_reabnormal": "1", "cell_x_name_cnt": "1", "cell_inlistwith_id": "1", "cell_x_id_notmat_days": "15", "m1": {"id_x_home_addr_cnt": "2", "cell_x_name_cnt": "1", "id_x_biz_addr_cnt": "3", "id_x_name_cnt": "1", "home_addr_x_id_cnt": "3", "cell_x_mail_cnt": "0", "linkman_cell_x_tel_home_cnt": "2", "tel_home_x_home_addr_cnt": "4", "tel_home_x_cell_cnt": "2", "id_x_cell_cnt": "0", "linkman_cell_x_id_cnt": "2", "cell_x_home_addr_cnt": "5", "tel_home_x_id_cnt": "4", "id_x_tel_home_cnt": "1", "id_x_mail_cnt": "0", "home_addr_x_cell_cnt": "3", "cell_x_id_cnt": "0", "cell_x_tel_home_cnt": "4", "linkman_cell_x_cell_cnt": "2", "cell_x_biz_addr_cnt": "2", "home_addr_x_tel_home_cnt": "5"}, "m3": {"id_x_home_addr_cnt": "2", "cell_x_name_cnt": "1", "id_x_biz_addr_cnt": "3", "id_x_name_cnt": "1", "home_addr_x_id_cnt": "3", "cell_x_mail_cnt": "0", "linkman_cell_x_tel_home_cnt": "2", "tel_home_x_home_addr_cnt": "4", "tel_home_x_cell_cnt": "2", "id_x_cell_cnt": "0", "linkman_cell_x_id_cnt": "2", "cell_x_home_addr_cnt": "5", "tel_home_x_id_cnt": "4", "id_x_tel_home_cnt": "1", "id_x_mail_cnt": "0", "home_addr_x_cell_cnt": "3", "cell_x_id_cnt": "0", "cell_x_tel_home_cnt": "4", "linkman_cell_x_cell_cnt": "2", "cell_x_biz_addr_cnt": "2", "home_addr_x_tel_home_cnt": "5"}, "m6": {"id_x_home_addr_cnt": "2", "cell_x_name_cnt": "1", "id_x_biz_addr_cnt": "3", "id_x_name_cnt": "1", "home_addr_x_id_cnt": "3", "cell_x_mail_cnt": "0", "linkman_cell_x_tel_home_cnt": "2", "tel_home_x_home_addr_cnt": "4", "tel_home_x_cell_cnt": "2", "id_x_cell_cnt": "0", "linkman_cell_x_id_cnt": "2", "cell_x_home_addr_cnt": "5", "tel_home_x_id_cnt": "4", "id_x_tel_home_cnt": "1", "id_x_mail_cnt": "0", "home_addr_x_cell_cnt": "3", "cell_x_id_cnt": "0", "cell_x_tel_home_cnt": "4", "linkman_cell_x_cell_cnt": "2", "cell_x_biz_addr_cnt": "2", "home_addr_x_tel_home_cnt": "5"}, "allmatch_days": "1", "id_x_name_cnt": "1", "mail_is_reabnormal": "0", "cell_x_mail_cnt": "2", "m12": {"id_x_home_addr_cnt": "2", "cell_x_name_cnt": "1", "id_x_biz_addr_cnt": "3", "id_x_name_cnt": "1", "home_addr_x_id_cnt": "3", "cell_x_mail_cnt": "0", "linkman_cell_x_tel_home_cnt": "2", "tel_home_x_home_addr_cnt": "4", "tel_home_x_cell_cnt": "2", "id_x_cell_cnt": "0", "linkman_cell_x_id_cnt": "2", "cell_x_home_addr_cnt": "5", "tel_home_x_id_cnt": "4", "id_x_tel_home_cnt": "1", "id_x_mail_cnt": "0", "home_addr_x_cell_cnt": "3", "cell_x_id_cnt": "0", "cell_x_tel_home_cnt": "4", "linkman_cell_x_cell_cnt": "2", "cell_x_biz_addr_cnt": "2", "home_addr_x_tel_home_cnt": "5"}, "id_x_cell_cnt": "2", "cell_x_id_lastchg_days": "53", "id_x_mail_cnt": "2", "id_x_cell_lastchg_days": "53", "id_inlistwith_cell": "1", "id_x_cell_notmat_days": "30", "cell_x_id_cnt": "2", "id_is_reabnormal": "1"}, "Score": {"scoreafautofin": "81"}, "code": "00", "RiskStrategy": {"ScoreAf": {"scoreafautofin": "81", "decision": "Reject"}, "strategy_version": "1.1", "product_type": "100099", "final_decision": "Reject", "strategy_id": "STR_BR0011492", "Rule": {"decision": "Reject", "weight": "100"}, "product_name": "预置_打包反欺诈规则", "platform": "web", "scene": "lend"}, "swift_number": "3034679_20241231170719_0301", "ApplyLoanStr": {"d7": {"id": {"caoff": {"orgnum": "1", "allnum": "1"}, "oth": {"orgnum": "1", "allnum": "1"}, "bank": {"week_allnum": "5", "selfnum": "1", "orgnum": "2", "night_allnum": "5", "ret_allnum": "1", "week_orgnum": "6", "tra_allnum": "1", "night_orgnum": "6", "tra_orgnum": "3", "ret_orgnum": "4", "allnum": "2"}, "caon": {"orgnum": "1", "allnum": "1"}, "cooff": {"orgnum": "1", "allnum": "1"}, "af": {"orgnum": "1", "allnum": "1"}, "coon": {"orgnum": "1", "allnum": "1"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "night_allnum": "5", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "week_allnum": "5", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "week_orgnum": "6", "night_orgnum": "6", "autofin_orgnum": "1"}, "rel": {"orgnum": "1", "allnum": "1"}, "pdl": {"orgnum": "1", "allnum": "1"}}, "cell": {"caoff": {"orgnum": "1", "allnum": "1"}, "oth": {"orgnum": "1", "allnum": "1"}, "bank": {"week_allnum": "5", "selfnum": "1", "orgnum": "2", "night_allnum": "5", "ret_allnum": "1", "week_orgnum": "6", "tra_allnum": "1", "night_orgnum": "6", "tra_orgnum": "3", "ret_orgnum": "4", "allnum": "2"}, "caon": {"orgnum": "1", "allnum": "1"}, "cooff": {"orgnum": "1", "allnum": "1"}, "af": {"orgnum": "1", "allnum": "1"}, "coon": {"orgnum": "1", "allnum": "1"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "night_allnum": "5", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "week_allnum": "5", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "week_orgnum": "6", "night_orgnum": "6", "autofin_orgnum": "1"}, "rel": {"orgnum": "1", "allnum": "1"}, "pdl": {"orgnum": "1", "allnum": "1"}}}, "m1": {"id": {"caoff": {"orgnum": "1", "allnum": "1"}, "oth": {"orgnum": "1", "allnum": "1"}, "bank": {"week_allnum": "5", "selfnum": "1", "orgnum": "2", "night_allnum": "5", "ret_allnum": "1", "week_orgnum": "6", "tra_allnum": "1", "night_orgnum": "6", "tra_orgnum": "3", "ret_orgnum": "4", "allnum": "2"}, "caon": {"orgnum": "1", "allnum": "1"}, "cooff": {"orgnum": "1", "allnum": "1"}, "af": {"orgnum": "1", "allnum": "1"}, "coon": {"orgnum": "1", "allnum": "1"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "night_allnum": "5", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "week_allnum": "5", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "week_orgnum": "6", "night_orgnum": "6", "autofin_orgnum": "1"}, "rel": {"orgnum": "1", "allnum": "1"}, "pdl": {"orgnum": "1", "allnum": "1"}}, "cell": {"caoff": {"orgnum": "1", "allnum": "1"}, "oth": {"orgnum": "1", "allnum": "1"}, "bank": {"week_allnum": "5", "selfnum": "1", "orgnum": "2", "night_allnum": "5", "ret_allnum": "1", "week_orgnum": "6", "tra_allnum": "1", "night_orgnum": "6", "tra_orgnum": "3", "ret_orgnum": "4", "allnum": "2"}, "caon": {"orgnum": "1", "allnum": "1"}, "cooff": {"orgnum": "1", "allnum": "1"}, "af": {"orgnum": "1", "allnum": "1"}, "coon": {"orgnum": "1", "allnum": "1"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "night_allnum": "5", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "week_allnum": "5", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "week_orgnum": "6", "night_orgnum": "6", "autofin_orgnum": "1"}, "rel": {"orgnum": "1", "allnum": "1"}, "pdl": {"orgnum": "1", "allnum": "1"}}}, "m3": {"id": {"oth": {"orgnum": "1", "allnum": "1"}, "cooff": {"orgnum": "1", "allnum": "1"}, "af": {"orgnum": "1", "allnum": "1"}, "coon": {"orgnum": "1", "allnum": "1"}, "max_inteday": "1", "tot_mons": "1", "caoff": {"orgnum": "1", "allnum": "1"}, "bank": {"orgnum": "1", "max_inteday": "1", "tra_allnum": "1", "ret_orgnum": "4", "allnum": "2", "tot_mons": "1", "week_allnum": "5", "selfnum": "1", "night_allnum": "5", "ret_allnum": "1", "max_monnum": "2", "min_inteday": "1", "week_orgnum": "6", "night_orgnum": "6", "tra_orgnum": "3", "avg_monnum": "1.00", "min_monnum": "4"}, "caon": {"orgnum": "1", "allnum": "1"}, "min_inteday": "2", "max_monnum": "4", "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "night_allnum": "5", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "min_monnum": "4", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "max_inteday": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "tot_mons": "1", "week_allnum": "5", "else_orgnum": "1", "max_monnum": "2", "min_inteday": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "week_orgnum": "6", "night_orgnum": "6", "autofin_orgnum": "1", "avg_monnum": "1.00"}, "rel": {"orgnum": "1", "allnum": "1"}, "pdl": {"orgnum": "1", "allnum": "1"}, "avg_monnum": "1.00", "min_monnum": "2"}, "cell": {"oth": {"orgnum": "1", "allnum": "1"}, "cooff": {"orgnum": "1", "allnum": "1"}, "af": {"orgnum": "1", "allnum": "1"}, "coon": {"orgnum": "1", "allnum": "1"}, "max_inteday": "1", "tot_mons": "1", "caoff": {"orgnum": "1", "allnum": "1"}, "bank": {"orgnum": "1", "max_inteday": "1", "tra_allnum": "1", "ret_orgnum": "4", "allnum": "2", "tot_mons": "1", "week_allnum": "5", "selfnum": "1", "night_allnum": "5", "ret_allnum": "1", "max_monnum": "2", "min_inteday": "1", "week_orgnum": "6", "night_orgnum": "6", "tra_orgnum": "3", "avg_monnum": "1.00", "min_monnum": "4"}, "caon": {"orgnum": "1", "allnum": "1"}, "min_inteday": "2", "max_monnum": "4", "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "night_allnum": "5", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "min_monnum": "4", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "max_inteday": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "tot_mons": "1", "week_allnum": "5", "else_orgnum": "1", "max_monnum": "2", "min_inteday": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "week_orgnum": "6", "night_orgnum": "6", "autofin_orgnum": "1", "avg_monnum": "1.00"}, "rel": {"orgnum": "1", "allnum": "1"}, "pdl": {"orgnum": "1", "allnum": "1"}, "avg_monnum": "1.00", "min_monnum": "2"}}, "fst": {"id": {"bank": {"inteday": "1"}, "nbank": {"inteday": "1"}}, "cell": {"bank": {"inteday": "1"}, "nbank": {"inteday": "1"}}}, "d15": {"id": {"caoff": {"orgnum": "1", "allnum": "1"}, "oth": {"orgnum": "1", "allnum": "1"}, "bank": {"week_allnum": "5", "selfnum": "1", "orgnum": "2", "night_allnum": "5", "ret_allnum": "1", "week_orgnum": "6", "tra_allnum": "1", "night_orgnum": "6", "tra_orgnum": "3", "ret_orgnum": "4", "allnum": "2"}, "caon": {"orgnum": "1", "allnum": "1"}, "cooff": {"orgnum": "1", "allnum": "1"}, "af": {"orgnum": "1", "allnum": "1"}, "coon": {"orgnum": "1", "allnum": "1"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "night_allnum": "5", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "week_allnum": "5", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "week_orgnum": "6", "night_orgnum": "6", "autofin_orgnum": "1"}, "rel": {"orgnum": "1", "allnum": "1"}, "pdl": {"orgnum": "1", "allnum": "1"}}, "cell": {"caoff": {"orgnum": "1", "allnum": "1"}, "oth": {"orgnum": "1", "allnum": "1"}, "bank": {"week_allnum": "5", "selfnum": "1", "orgnum": "2", "night_allnum": "5", "ret_allnum": "1", "week_orgnum": "6", "tra_allnum": "1", "night_orgnum": "6", "tra_orgnum": "3", "ret_orgnum": "4", "allnum": "2"}, "caon": {"orgnum": "1", "allnum": "1"}, "cooff": {"orgnum": "1", "allnum": "1"}, "af": {"orgnum": "1", "allnum": "1"}, "coon": {"orgnum": "1", "allnum": "1"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "night_allnum": "5", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "week_allnum": "5", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "week_orgnum": "6", "night_orgnum": "6", "autofin_orgnum": "1"}, "rel": {"orgnum": "1", "allnum": "1"}, "pdl": {"orgnum": "1", "allnum": "1"}}}, "m6": {"id": {"oth": {"orgnum": "1", "allnum": "1"}, "cooff": {"orgnum": "1", "allnum": "1"}, "af": {"orgnum": "1", "allnum": "1"}, "coon": {"orgnum": "1", "allnum": "1"}, "max_inteday": "1", "tot_mons": "1", "caoff": {"orgnum": "1", "allnum": "1"}, "bank": {"orgnum": "1", "max_inteday": "1", "tra_allnum": "1", "ret_orgnum": "4", "allnum": "2", "tot_mons": "1", "week_allnum": "5", "selfnum": "1", "night_allnum": "5", "ret_allnum": "1", "max_monnum": "2", "min_inteday": "1", "week_orgnum": "6", "night_orgnum": "6", "tra_orgnum": "3", "avg_monnum": "1.00", "min_monnum": "4"}, "caon": {"orgnum": "1", "allnum": "1"}, "min_inteday": "2", "max_monnum": "4", "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "night_allnum": "5", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "min_monnum": "4", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "max_inteday": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "tot_mons": "1", "week_allnum": "5", "else_orgnum": "1", "max_monnum": "2", "min_inteday": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "week_orgnum": "6", "night_orgnum": "6", "autofin_orgnum": "1", "avg_monnum": "1.00"}, "rel": {"orgnum": "1", "allnum": "1"}, "pdl": {"orgnum": "1", "allnum": "1"}, "avg_monnum": "1.00", "min_monnum": "2"}, "cell": {"oth": {"orgnum": "1", "allnum": "1"}, "cooff": {"orgnum": "1", "allnum": "1"}, "af": {"orgnum": "1", "allnum": "1"}, "coon": {"orgnum": "1", "allnum": "1"}, "max_inteday": "1", "tot_mons": "1", "caoff": {"orgnum": "1", "allnum": "1"}, "bank": {"orgnum": "1", "max_inteday": "1", "tra_allnum": "1", "ret_orgnum": "4", "allnum": "2", "tot_mons": "1", "week_allnum": "5", "selfnum": "1", "night_allnum": "5", "ret_allnum": "1", "max_monnum": "2", "min_inteday": "1", "week_orgnum": "6", "night_orgnum": "6", "tra_orgnum": "3", "avg_monnum": "1.00", "min_monnum": "4"}, "caon": {"orgnum": "1", "allnum": "1"}, "min_inteday": "2", "max_monnum": "4", "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "night_allnum": "5", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "min_monnum": "4", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "max_inteday": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "tot_mons": "1", "week_allnum": "5", "else_orgnum": "1", "max_monnum": "2", "min_inteday": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "week_orgnum": "6", "night_orgnum": "6", "autofin_orgnum": "1", "avg_monnum": "1.00"}, "rel": {"orgnum": "1", "allnum": "1"}, "pdl": {"orgnum": "1", "allnum": "1"}, "avg_monnum": "1.00", "min_monnum": "2"}}, "lst": {"id": {"bank": {"inteday": "1", "consnum": "1", "csinteday": "1"}, "nbank": {"inteday": "1", "consnum": "1", "csinteday": "1"}}, "cell": {"bank": {"inteday": "1", "consnum": "1", "csinteday": "1"}, "nbank": {"inteday": "1", "consnum": "1", "csinteday": "1"}}}, "m12": {"id": {"oth": {"orgnum": "1", "allnum": "1"}, "cooff": {"orgnum": "1", "allnum": "1"}, "af": {"orgnum": "1", "allnum": "1"}, "coon": {"orgnum": "1", "allnum": "1"}, "max_inteday": "1", "tot_mons": "1", "caoff": {"orgnum": "1", "allnum": "1"}, "bank": {"orgnum": "1", "max_inteday": "1", "tra_allnum": "1", "ret_orgnum": "4", "allnum": "2", "tot_mons": "1", "week_allnum": "5", "selfnum": "1", "night_allnum": "5", "ret_allnum": "1", "max_monnum": "2", "min_inteday": "1", "week_orgnum": "6", "night_orgnum": "6", "tra_orgnum": "3", "avg_monnum": "1.00", "min_monnum": "4"}, "caon": {"orgnum": "1", "allnum": "1"}, "min_inteday": "2", "max_monnum": "4", "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "night_allnum": "5", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "min_monnum": "4", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "max_inteday": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "tot_mons": "1", "week_allnum": "5", "else_orgnum": "1", "max_monnum": "2", "min_inteday": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "week_orgnum": "6", "night_orgnum": "6", "autofin_orgnum": "1", "avg_monnum": "1.00"}, "rel": {"orgnum": "1", "allnum": "1"}, "pdl": {"orgnum": "1", "allnum": "1"}, "avg_monnum": "1.00", "min_monnum": "2"}, "cell": {"oth": {"orgnum": "1", "allnum": "1"}, "cooff": {"orgnum": "1", "allnum": "1"}, "af": {"orgnum": "1", "allnum": "1"}, "coon": {"orgnum": "1", "allnum": "1"}, "max_inteday": "1", "tot_mons": "1", "caoff": {"orgnum": "1", "allnum": "1"}, "bank": {"orgnum": "1", "max_inteday": "1", "tra_allnum": "1", "ret_orgnum": "4", "allnum": "2", "tot_mons": "1", "week_allnum": "5", "selfnum": "1", "night_allnum": "5", "ret_allnum": "1", "max_monnum": "2", "min_inteday": "1", "week_orgnum": "6", "night_orgnum": "6", "tra_orgnum": "3", "avg_monnum": "1.00", "min_monnum": "4"}, "caon": {"orgnum": "1", "allnum": "1"}, "min_inteday": "2", "max_monnum": "4", "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "night_allnum": "5", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "min_monnum": "4", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "max_inteday": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "tot_mons": "1", "week_allnum": "5", "else_orgnum": "1", "max_monnum": "2", "min_inteday": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "week_orgnum": "6", "night_orgnum": "6", "autofin_orgnum": "1", "avg_monnum": "1.00"}, "rel": {"orgnum": "1", "allnum": "1"}, "pdl": {"orgnum": "1", "allnum": "1"}, "avg_monnum": "1.00", "min_monnum": "2"}}}, "Rule": {"result": {"final_weight": "100", "final_decision": "Reject"}, "hit_rules": {"ruleexecutionpro": {"FCX0000431": {"weight": "100", "name_rule": "为法院失信被执行人且存在未履行的失信案件"}, "FCX0000436": {"weight": "60", "name_rule": "曾为法院被执行人但已全部结案"}, "FCX0000437": {"weight": "90", "name_rule": "为法院失信被执行人但所有案件均已履行"}, "FCX0000438": {"weight": "90", "name_rule": "命中曝光台"}, "FCX0000439": {"weight": "80", "name_rule": "命中曝光台已下架"}, "FCX0000432": {"weight": "90", "name_rule": "为法院失信被执行人且存在未履行的失信案件但已下架"}, "FCX0000433": {"weight": "80", "name_rule": "为法院被执行人且未全部结案"}, "FCX0000434": {"weight": "75", "name_rule": "为法院被执行人且未全部结案但已下架且五年内立案"}, "FCX0000435": {"weight": "70", "name_rule": "为法院被执行人且未全部结案但已下架且五年前立案"}}, "rule_d_debtrepaystress": {"FCI001": {"weight": "100", "name_rule": "偿债压力极高"}, "FCI002": {"weight": "80", "name_rule": "偿债压力较高"}}, "ruleinforelation_revoloan": {"CBB004": {"weight": "60", "name_rule": "身份证关联查询可疑信息较多"}, "CBB003": {"weight": "80", "name_rule": "身份证关联查询可疑信息极多"}, "CBB002": {"weight": "60", "name_rule": "手机号关联查询可疑信息较多"}, "CBB001": {"weight": "80", "name_rule": "手机号关联查询可疑信息极多"}}, "ruleapplyloan_autofin": {"CAC0000678": {"weight": "68", "name_rule": "近期申请产品类型数较多"}, "CAC0000711": {"weight": "43", "name_rule": "长期申请产品类型数极少"}, "CAC0000679": {"weight": "68", "name_rule": "长期申请产品类型数较多"}, "CAC0000676": {"weight": "68", "name_rule": "近期申请机构类型数较多"}, "CAC0000677": {"weight": "68", "name_rule": "长期申请机构类型数较多"}, "CAC0000710": {"weight": "43", "name_rule": "近期申请产品类型数极少"}, "CAC0000674": {"weight": "76", "name_rule": "某月申请较为频繁"}, "CAC0000675": {"weight": "74", "name_rule": "某月申请其他业务较为频繁"}, "CAC0000672": {"weight": "72", "name_rule": "夜间在非银机构申请较为频繁"}, "CAC0000673": {"weight": "77", "name_rule": "周末在非银机构申请较为频繁"}, "CAC0000670": {"weight": "76", "name_rule": "近期申请线上现金分期较为频繁"}, "CAC0000671": {"weight": "77", "name_rule": "长期申请线上现金分期较为频繁"}, "CAC0000649": {"weight": "87", "name_rule": "长期在非银机构申请极为频繁"}, "CAC0000647": {"weight": "85", "name_rule": "申请当日在非银机构申请极为频繁"}, "CAC0000648": {"weight": "91", "name_rule": "近期在非银机构申请极为频繁"}, "CAC0000645": {"weight": "91", "name_rule": "在小贷机构申请极为频繁"}, "CAC0000689": {"weight": "52", "name_rule": "夜间在非银机构申请较少"}, "CAC0000646": {"weight": "91", "name_rule": "在网络小贷机构申请极为频繁"}, "CAC0000687": {"weight": "62", "name_rule": "近期申请线上现金分期较少"}, "CAC0000688": {"weight": "63", "name_rule": "长期申请线上现金分期较少"}, "CAC0000685": {"weight": "60", "name_rule": "长期申请线上小额现金贷较少"}, "CAC0000686": {"weight": "60", "name_rule": "申请线下消费分期较少"}, "CAC0000683": {"weight": "62", "name_rule": "长期在非银机构申请较少"}, "CAC0000684": {"weight": "64", "name_rule": "近期申请线上小额现金贷较少"}, "CAC0000681": {"weight": "62", "name_rule": "在网络小贷机构申请较少"}, "CAC0000682": {"weight": "63", "name_rule": "近期在非银机构申请较少"}, "CAC0000680": {"weight": "53", "name_rule": "在小贷机构申请较少"}, "CAC0000658": {"weight": "91", "name_rule": "某月申请其他业务极为频繁"}, "CAC0000659": {"weight": "89", "name_rule": "近期申请机构类型数极多"}, "CAC0000656": {"weight": "91", "name_rule": "周末在非银机构申请极为频繁"}, "CAC0000657": {"weight": "91", "name_rule": "某月申请极为频繁"}, "CAC0000654": {"weight": "91", "name_rule": "长期申请线上现金分期极为频繁"}, "CAC0000698": {"weight": "47", "name_rule": "在网络小贷机构申请极少"}, "CAC0000655": {"weight": "91", "name_rule": "夜间在非银机构申请极为频繁"}, "CAC0000699": {"weight": "46", "name_rule": "近期在非银机构申请极少"}, "CAC0000652": {"weight": "91", "name_rule": "申请线下消费分期极为频繁"}, "CAC0000696": {"weight": "57", "name_rule": "长期申请产品类型数较少"}, "CAC0000653": {"weight": "91", "name_rule": "近期申请线上现金分期极为频繁"}, "CAC0000697": {"weight": "41", "name_rule": "在小贷机构申请极少"}, "CAC0000650": {"weight": "91", "name_rule": "近期申请线上小额现金贷极为频繁"}, "CAC0000694": {"weight": "57", "name_rule": "长期申请机构类型数较少"}, "CAC0000651": {"weight": "91", "name_rule": "长期申请线上小额现金贷极为频繁"}, "CAC0000695": {"weight": "57", "name_rule": "近期申请产品类型数较少"}, "CAC0000692": {"weight": "63", "name_rule": "某月申请其他业务较少"}, "CAC0000693": {"weight": "57", "name_rule": "近期申请机构类型数较少"}, "CAC0000690": {"weight": "64", "name_rule": "周末在非银机构申请较少"}, "CAC0000691": {"weight": "60", "name_rule": "某月申请较少"}, "CAC0000704": {"weight": "43", "name_rule": "长期申请线上现金分期极少"}, "CAC0000705": {"weight": "43", "name_rule": "周末在非银机构申请极少"}, "CAC0000669": {"weight": "68", "name_rule": "申请线下消费分期较为频繁"}, "CAC0000702": {"weight": "44", "name_rule": "长期申请线上小额现金贷极少"}, "CAC0000703": {"weight": "41", "name_rule": "近期申请线上现金分期极少"}, "CAC0000667": {"weight": "76", "name_rule": "近期申请线上小额现金贷较为频繁"}, "CAC0000700": {"weight": "47", "name_rule": "长期在非银机构申请极少"}, "CAC0000668": {"weight": "70", "name_rule": "长期申请线上小额现金贷较为频繁"}, "CAC0000701": {"weight": "42", "name_rule": "近期申请线上小额现金贷极少"}, "CAC0000665": {"weight": "70", "name_rule": "近期在非银机构申请较为频繁"}, "CAC0000666": {"weight": "77", "name_rule": "长期在非银机构申请较为频繁"}, "CAC0000708": {"weight": "43", "name_rule": "近期申请机构类型数极少"}, "CAC0000709": {"weight": "43", "name_rule": "长期申请机构类型数极少"}, "CAC0000706": {"weight": "42", "name_rule": "某月申请极少"}, "CAC0000707": {"weight": "48", "name_rule": "某月申请其他业务极少"}, "CAC0000663": {"weight": "67", "name_rule": "在小贷机构申请较为频繁"}, "CAC0000664": {"weight": "72", "name_rule": "在网络小贷机构申请较为频繁"}, "CAC0000661": {"weight": "89", "name_rule": "近期申请产品类型数极多"}, "CAC0000662": {"weight": "89", "name_rule": "长期申请产品类型数极多"}, "CAC0000660": {"weight": "89", "name_rule": "长期申请机构类型数极多"}}}}, "ExecutionPro": {"execut10": {"casenum": "(2015)咸执字第00959号", "dataType": "最高法执行", "sign": "0", "matchRatio": "", "court": "咸安区人民法院", "datatypeid": "103", "eventLevel": "", "money": "2000", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "name": "张三", "signalRating": "1", "time": "2018年11月05日", "statute": ""}, "execut8": {"casenum": "(2015)咸执字第00959号", "dataType": "最高法执行", "sign": "0", "matchRatio": "", "court": "咸安区人民法院", "datatypeid": "103", "eventLevel": "", "money": "2000", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "name": "张三", "signalRating": "1", "time": "2018年11月05日", "statute": ""}, "execut9": {"casenum": "(2015)咸执字第00959号", "dataType": "最高法执行", "sign": "0", "matchRatio": "", "court": "咸安区人民法院", "datatypeid": "103", "eventLevel": "", "money": "2000", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "name": "张三", "signalRating": "1", "time": "2018年11月05日", "statute": ""}, "execut1": {"casenum": "(2015)咸执字第00959号", "dataType": "最高法执行", "sign": "0", "matchRatio": "", "court": "咸安区人民法院", "datatypeid": "103", "eventLevel": "", "money": "2000", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "name": "张三", "signalRating": "1", "time": "2018年11月05日", "statute": ""}, "execut2": {"casenum": "(2015)咸执字第00959号", "dataType": "最高法执行", "sign": "0", "matchRatio": "", "court": "咸安区人民法院", "datatypeid": "103", "eventLevel": "", "money": "2000", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "name": "张三", "signalRating": "1", "time": "2018年11月05日", "statute": ""}, "execut3": {"casenum": "(2015)咸执字第00959号", "dataType": "最高法执行", "sign": "0", "matchRatio": "", "court": "咸安区人民法院", "datatypeid": "103", "eventLevel": "", "money": "2000", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "name": "张三", "signalRating": "1", "time": "2018年11月05日", "statute": ""}, "execut4": {"casenum": "(2015)咸执字第00959号", "dataType": "最高法执行", "sign": "0", "matchRatio": "", "court": "咸安区人民法院", "datatypeid": "103", "eventLevel": "", "money": "2000", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "name": "张三", "signalRating": "1", "time": "2018年11月05日", "statute": ""}, "execut5": {"casenum": "(2015)咸执字第00959号", "dataType": "最高法执行", "sign": "0", "matchRatio": "", "court": "咸安区人民法院", "datatypeid": "103", "eventLevel": "", "money": "2000", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "name": "张三", "signalRating": "1", "time": "2018年11月05日", "statute": ""}, "execut6": {"casenum": "(2015)咸执字第00959号", "dataType": "最高法执行", "sign": "0", "matchRatio": "", "court": "咸安区人民法院", "datatypeid": "103", "eventLevel": "", "money": "2000", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "name": "张三", "signalRating": "1", "time": "2018年11月05日", "statute": ""}, "execut7": {"casenum": "(2015)咸执字第00959号", "dataType": "最高法执行", "sign": "0", "matchRatio": "", "court": "咸安区人民法院", "datatypeid": "103", "eventLevel": "", "money": "2000", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "name": "张三", "signalRating": "1", "time": "2018年11月05日", "statute": ""}, "bad3": {"posttime": "2018年11月05日", "address": "湖北省咸宁市咸安区花园小区", "casenum": "(2015)咸执字第00959号", "sign": "0", "matchRatio": "", "court": "咸安区人民法院", "datatypeid": "102", "basecompany": "", "eventLevel": "", "performance": "", "datatype": "失信被执行人", "obligation": "由王亮赔偿12.683012万元", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "name": "张三", "signalRating": "1", "time": "2018年11月05日", "concretesituation": "其他有履行能力而拒不履行生效法律文书确定义务", "base": "(2014)鄂咸安民初字第04374号"}, "bad4": {"posttime": "2018年11月05日", "address": "湖北省咸宁市咸安区花园小区", "casenum": "(2015)咸执字第00959号", "sign": "0", "matchRatio": "", "court": "咸安区人民法院", "datatypeid": "102", "basecompany": "", "eventLevel": "", "performance": "", "datatype": "失信被执行人", "obligation": "由王亮赔偿12.683012万元", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "name": "张三", "signalRating": "1", "time": "2018年11月05日", "concretesituation": "其他有履行能力而拒不履行生效法律文书确定义务", "base": "(2014)鄂咸安民初字第04374号"}, "bad1": {"posttime": "2018年11月05日", "address": "湖北省咸宁市咸安区花园小区", "casenum": "(2015)咸执字第00959号", "sign": "0", "matchRatio": "", "court": "咸安区人民法院", "datatypeid": "102", "basecompany": "", "eventLevel": "", "performance": "", "datatype": "失信被执行人", "obligation": "由王亮赔偿12.683012万元", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "name": "张三", "signalRating": "1", "time": "2018年11月05日", "concretesituation": "其他有履行能力而拒不履行生效法律文书确定义务", "base": "(2014)鄂咸安民初字第04374号"}, "bad2": {"posttime": "2018年11月05日", "address": "湖北省咸宁市咸安区花园小区", "casenum": "(2015)咸执字第00959号", "sign": "0", "matchRatio": "", "court": "咸安区人民法院", "datatypeid": "102", "basecompany": "", "eventLevel": "", "performance": "", "datatype": "失信被执行人", "obligation": "由王亮赔偿12.683012万元", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "name": "张三", "signalRating": "1", "time": "2018年11月05日", "concretesituation": "其他有履行能力而拒不履行生效法律文书确定义务", "base": "(2014)鄂咸安民初字第04374号"}, "bgt9": {"address": "", "yiju": "", "pname": "张三", "sortTime": "2018年11月05日", "dataType": "曝光台", "sign": "0", "matchRatio": "", "body": "", "court": "密山", "execMoney": "", "caseNo": "（2018）黑0382执758号", "datatypeid": "104", "eventLevel": "", "caseCause": "", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "signalRating": "1"}, "bgt7": {"address": "", "yiju": "", "pname": "张三", "sortTime": "2018年11月05日", "dataType": "曝光台", "sign": "0", "matchRatio": "", "body": "", "court": "密山", "execMoney": "", "caseNo": "（2018）黑0382执758号", "datatypeid": "104", "eventLevel": "", "caseCause": "", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "signalRating": "1"}, "bgt8": {"address": "", "yiju": "", "pname": "张三", "sortTime": "2018年11月05日", "dataType": "曝光台", "sign": "0", "matchRatio": "", "body": "", "court": "密山", "execMoney": "", "caseNo": "（2018）黑0382执758号", "datatypeid": "104", "eventLevel": "", "caseCause": "", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "signalRating": "1"}, "bgt5": {"address": "", "yiju": "", "pname": "张三", "sortTime": "2018年11月05日", "dataType": "曝光台", "sign": "0", "matchRatio": "", "body": "", "court": "密山", "execMoney": "", "caseNo": "（2018）黑0382执758号", "datatypeid": "104", "eventLevel": "", "caseCause": "", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "signalRating": "1"}, "bgt6": {"address": "", "yiju": "", "pname": "张三", "sortTime": "2018年11月05日", "dataType": "曝光台", "sign": "0", "matchRatio": "", "body": "", "court": "密山", "execMoney": "", "caseNo": "（2018）黑0382执758号", "datatypeid": "104", "eventLevel": "", "caseCause": "", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "signalRating": "1"}, "bgt3": {"address": "", "yiju": "", "pname": "张三", "sortTime": "2018年11月05日", "dataType": "曝光台", "sign": "0", "matchRatio": "", "body": "", "court": "密山", "execMoney": "", "caseNo": "（2018）黑0382执758号", "datatypeid": "104", "eventLevel": "", "caseCause": "", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "signalRating": "1"}, "bgt4": {"address": "", "yiju": "", "pname": "张三", "sortTime": "2018年11月05日", "dataType": "曝光台", "sign": "0", "matchRatio": "", "body": "", "court": "密山", "execMoney": "", "caseNo": "（2018）黑0382执758号", "datatypeid": "104", "eventLevel": "", "caseCause": "", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "signalRating": "1"}, "bgt1": {"address": "", "yiju": "", "pname": "张三", "sortTime": "2018年11月05日", "dataType": "曝光台", "sign": "0", "matchRatio": "", "body": "", "court": "密山", "execMoney": "", "caseNo": "（2018）黑0382执758号", "datatypeid": "104", "eventLevel": "", "caseCause": "", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "signalRating": "1"}, "bgt2": {"address": "", "yiju": "", "pname": "张三", "sortTime": "2018年11月05日", "dataType": "曝光台", "sign": "0", "matchRatio": "", "body": "", "court": "密山", "execMoney": "", "caseNo": "（2018）黑0382执758号", "datatypeid": "104", "eventLevel": "", "caseCause": "", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "signalRating": "1"}, "bad10": {"posttime": "2018年11月05日", "address": "湖北省咸宁市咸安区花园小区", "casenum": "(2015)咸执字第00959号", "sign": "0", "matchRatio": "", "court": "咸安区人民法院", "datatypeid": "102", "basecompany": "", "eventLevel": "", "performance": "", "datatype": "失信被执行人", "obligation": "由王亮赔偿12.683012万元", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "name": "张三", "signalRating": "1", "time": "2018年11月05日", "concretesituation": "其他有履行能力而拒不履行生效法律文书确定义务", "base": "(2014)鄂咸安民初字第04374号"}, "bad9": {"posttime": "2018年11月05日", "address": "湖北省咸宁市咸安区花园小区", "casenum": "(2015)咸执字第00959号", "sign": "0", "matchRatio": "", "court": "咸安区人民法院", "datatypeid": "102", "basecompany": "", "eventLevel": "", "performance": "", "datatype": "失信被执行人", "obligation": "由王亮赔偿12.683012万元", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "name": "张三", "signalRating": "1", "time": "2018年11月05日", "concretesituation": "其他有履行能力而拒不履行生效法律文书确定义务", "base": "(2014)鄂咸安民初字第04374号"}, "bgt10": {"address": "", "yiju": "", "pname": "张三", "sortTime": "2018年11月05日", "dataType": "曝光台", "sign": "0", "matchRatio": "", "body": "", "court": "密山", "execMoney": "", "caseNo": "（2018）黑0382执758号", "datatypeid": "104", "eventLevel": "", "caseCause": "", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "signalRating": "1"}, "bad7": {"posttime": "2018年11月05日", "address": "湖北省咸宁市咸安区花园小区", "casenum": "(2015)咸执字第00959号", "sign": "0", "matchRatio": "", "court": "咸安区人民法院", "datatypeid": "102", "basecompany": "", "eventLevel": "", "performance": "", "datatype": "失信被执行人", "obligation": "由王亮赔偿12.683012万元", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "name": "张三", "signalRating": "1", "time": "2018年11月05日", "concretesituation": "其他有履行能力而拒不履行生效法律文书确定义务", "base": "(2014)鄂咸安民初字第04374号"}, "bad8": {"posttime": "2018年11月05日", "address": "湖北省咸宁市咸安区花园小区", "casenum": "(2015)咸执字第00959号", "sign": "0", "matchRatio": "", "court": "咸安区人民法院", "datatypeid": "102", "basecompany": "", "eventLevel": "", "performance": "", "datatype": "失信被执行人", "obligation": "由王亮赔偿12.683012万元", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "name": "张三", "signalRating": "1", "time": "2018年11月05日", "concretesituation": "其他有履行能力而拒不履行生效法律文书确定义务", "base": "(2014)鄂咸安民初字第04374号"}, "bad5": {"posttime": "2018年11月05日", "address": "湖北省咸宁市咸安区花园小区", "casenum": "(2015)咸执字第00959号", "sign": "0", "matchRatio": "", "court": "咸安区人民法院", "datatypeid": "102", "basecompany": "", "eventLevel": "", "performance": "", "datatype": "失信被执行人", "obligation": "由王亮赔偿12.683012万元", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "name": "张三", "signalRating": "1", "time": "2018年11月05日", "concretesituation": "其他有履行能力而拒不履行生效法律文书确定义务", "base": "(2014)鄂咸安民初字第04374号"}, "bad6": {"posttime": "2018年11月05日", "address": "湖北省咸宁市咸安区花园小区", "casenum": "(2015)咸执字第00959号", "sign": "0", "matchRatio": "", "court": "咸安区人民法院", "datatypeid": "102", "basecompany": "", "eventLevel": "", "performance": "", "datatype": "失信被执行人", "obligation": "由王亮赔偿12.683012万元", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "name": "张三", "signalRating": "1", "time": "2018年11月05日", "concretesituation": "其他有履行能力而拒不履行生效法律文书确定义务", "base": "(2014)鄂咸安民初字第04374号"}}, "ApplyLoanMon": {"m1": {"lm_cell": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}, "id": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}, "cell": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}}, "m2": {"lm_cell": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}, "id": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}, "cell": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}}, "m3": {"lm_cell": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}, "id": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}, "cell": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}}, "m4": {"lm_cell": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}, "id": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}, "cell": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}}, "m5": {"lm_cell": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}, "id": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}, "cell": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}}, "d15": {"lm_cell": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}, "id": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}, "cell": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}}, "m6": {"lm_cell": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}, "id": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}, "cell": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}}, "m7": {"lm_cell": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}, "id": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}, "cell": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}}, "m8": {"lm_cell": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}, "id": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}, "cell": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}}, "m9": {"lm_cell": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}, "id": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}, "cell": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}}, "m11": {"lm_cell": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}, "id": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}, "cell": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}}, "m10": {"lm_cell": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}, "id": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}, "cell": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}}, "m12": {"lm_cell": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}, "id": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}, "cell": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}}, "d7": {"lm_cell": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}, "id": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}, "cell": {"bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "else_orgnum": "1", "nsloan_allnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}}}}, "Flag": {"score": "1", "riskstrategy": "1", "applyloanstr": "1", "ruleexecutionpro": "1", "rule_d_debtrepaystress": "1", "applyloan_d": "1", "executionpro": "1", "ruleinforelation_revoloan": "1", "inforelation": "1", "debtrepaystress": "1", "ruleapplyloan_autofin": "1", "applyloanmon": "1"}, "ApplyLoan_d": {"id_x_cell_num": "2", "id": {"caoff": {"orgnum": "1", "allnum": "1"}, "oth": {"orgnum": "1", "allnum": "1"}, "bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "caon": {"orgnum": "1", "allnum": "1"}, "cooff": {"orgnum": "1", "allnum": "1"}, "af": {"orgnum": "1", "allnum": "1"}, "coon": {"orgnum": "1", "allnum": "1"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "ca_off_orgnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cf_on_allnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "ca_off_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "ca_on_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "cf_off_allnum": "1", "else_orgnum": "1", "cf_on_orgnum": "1", "nsloan_allnum": "1", "ca_on_orgnum": "1", "cf_off_orgnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}, "rel": {"orgnum": "1", "allnum": "1"}, "pdl": {"orgnum": "1", "allnum": "1"}}, "cell": {"caoff": {"orgnum": "1", "allnum": "1"}, "oth": {"orgnum": "1", "allnum": "1"}, "bank": {"selfnum": "1", "orgnum": "1", "ret_allnum": "4", "tra_allnum": "3", "tra_orgnum": "2", "ret_orgnum": "3", "allnum": "2"}, "caon": {"orgnum": "1", "allnum": "1"}, "cooff": {"orgnum": "1", "allnum": "1"}, "af": {"orgnum": "1", "allnum": "1"}, "coon": {"orgnum": "1", "allnum": "1"}, "nbank": {"p2p_orgnum": "1", "cf_orgnum": "1", "nsloan_orgnum": "1", "orgnum": "2", "sloan_allnum": "1", "ca_off_orgnum": "1", "com_allnum": "1", "oth_allnum": "1", "selfnum": "1", "cf_on_allnum": "1", "cons_orgnum": "1", "finlea_allnum": "1", "oth_orgnum": "1", "mc_allnum": "1", "ca_off_allnum": "1", "sloan_orgnum": "1", "cons_allnum": "1", "mc_orgnum": "1", "com_orgnum": "1", "ca_allnum": "1", "ca_on_allnum": "1", "cf_allnum": "1", "p2p_allnum": "2", "autofin_allnum": "1", "else_allnum": "1", "allnum": "3", "finlea_orgnum": "1", "cf_off_allnum": "1", "else_orgnum": "1", "cf_on_orgnum": "1", "nsloan_allnum": "1", "ca_on_orgnum": "1", "cf_off_orgnum": "1", "ca_orgnum": "1", "autofin_orgnum": "1"}, "rel": {"orgnum": "1", "allnum": "1"}, "pdl": {"orgnum": "1", "allnum": "1"}}}, "DebtRepayStress": {"nodebtscore": "80"}}