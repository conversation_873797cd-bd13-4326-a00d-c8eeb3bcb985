 static {
        // 按身份证号查询近7天申请线上小额现金贷的次数
        JSON_PATH_MAPPERS.put("id_online_pdl_d7_cnt", "$.ApplyLoanStr.d7.id.pdl.allnum");
        // 按身份证号查询近7天申请线上小额现金贷的机构数
        JSON_PATH_MAPPERS.put("id_online_pdl_d7_org", "$.ApplyLoanStr.d7.id.pdl.orgnum");
        // 按身份证号查询近7天申请线上现金分期的次数
        JSON_PATH_MAPPERS.put("id_online_cash_d7_cnt", "$.ApplyLoanStr.d7.id.caon.allnum");
        // 按身份证号查询近7天申请线上现金分期的机构数
        JSON_PATH_MAPPERS.put("id_online_cash_d7_org", "$.ApplyLoanStr.d7.id.caon.orgnum");
        // 按身份证号查询近7天申请信用卡类信用卡的次数
        JSON_PATH_MAPPERS.put("id_credit_card_d7_cnt", "$.ApplyLoanStr.d7.id.rel.allnum");
        // 按身份证号查询近7天申请信用卡类信用卡的机构数
        JSON_PATH_MAPPERS.put("id_credit_card_d7_org", "$.ApplyLoanStr.d7.id.rel.orgnum");
        // 按身份证号查询近7天申请线下现金分期的次数
        JSON_PATH_MAPPERS.put("id_offline_cash_d7_cnt", "$.ApplyLoanStr.d7.id.caoff.allnum");
        // 按身份证号查询近7天申请线下现金分期的机构数
        JSON_PATH_MAPPERS.put("id_offline_cash_d7_org", "$.ApplyLoanStr.d7.id.caoff.orgnum");
        // 按身份证号查询近7天申请线下消费分期的次数
        JSON_PATH_MAPPERS.put("id_offline_consume_d7_cnt", "$.ApplyLoanStr.d7.id.cooff.allnum");
        // 按身份证号查询近7天申请线下消费分期的机构数
        JSON_PATH_MAPPERS.put("id_offline_consume_d7_org", "$.ApplyLoanStr.d7.id.cooff.orgnum");
        // 按身份证号查询近7天申请线上消费分期的次数
        JSON_PATH_MAPPERS.put("id_online_consume_d7_cnt", "$.ApplyLoanStr.d7.id.coon.allnum");
        // 按身份证号查询近7天申请线上消费分期的机构数
        JSON_PATH_MAPPERS.put("id_online_consume_d7_org", "$.ApplyLoanStr.d7.id.coon.orgnum");
        // 按身份证号查询近7天在银行机构申请次数
        JSON_PATH_MAPPERS.put("id_bank_d7_cnt", "$.ApplyLoanStr.d7.id.bank.allnum");
        // 按身份证号查询近7天在银行机构传统银行申请次数
        JSON_PATH_MAPPERS.put("id_bank_trad_d7_cnt", "$.ApplyLoanStr.d7.id.bank.tra_allnum");
        // 按身份证号查询近7天在银行机构网络零售银行申请次数
        JSON_PATH_MAPPERS.put("id_bank_retail_d7_cnt", "$.ApplyLoanStr.d7.id.bank.ret_allnum");
        // 按身份证号查询近7天在银行机构申请机构数
        JSON_PATH_MAPPERS.put("id_bank_d7_org", "$.ApplyLoanStr.d7.id.bank.orgnum");
        // 按身份证号查询近7天在银行机构传统银行申请机构数
        JSON_PATH_MAPPERS.put("id_bank_trad_d7_org", "$.ApplyLoanStr.d7.id.bank.tra_orgnum");
        // 按身份证号查询近7天在银行机构网络零售银行申请机构数
        JSON_PATH_MAPPERS.put("id_bank_retail_d7_org", "$.ApplyLoanStr.d7.id.bank.ret_orgnum");
        // 按身份证号查询近7天在银行机构夜间申请次数
        JSON_PATH_MAPPERS.put("id_bank_night_d7_cnt", "$.ApplyLoanStr.d7.id.bank.night_allnum");
        // 按身份证号查询近7天在银行机构夜间申请机构数
        JSON_PATH_MAPPERS.put("id_bank_night_d7_org", "$.ApplyLoanStr.d7.id.bank.night_orgnum");
        // 按身份证号查询近7天在非银机构申请次数
        JSON_PATH_MAPPERS.put("id_non_bank_d7_cnt", "$.ApplyLoanStr.d7.id.nbank.allnum");
        // 按身份证号查询近7天在非银机构小贷机构申请次数
        JSON_PATH_MAPPERS.put("id_non_bank_mc_d7_cnt", "$.ApplyLoanStr.d7.id.nbank.mc_allnum");
        // 按身份证号查询近7天在非银机构现金类分期机构申请次数
        JSON_PATH_MAPPERS.put("id_non_bank_cash_d7_cnt", "$.ApplyLoanStr.d7.id.nbank.ca_allnum");
        // 按身份证号查询近7天在非银机构消费类分期机构申请次数
        JSON_PATH_MAPPERS.put("id_non_bank_consume_d7_cnt", "$.ApplyLoanStr.d7.id.nbank.cf_allnum");
        // 按身份证号查询近7天在非银机构代偿类分期机构申请次数
        JSON_PATH_MAPPERS.put("id_non_bank_repay_d7_cnt", "$.ApplyLoanStr.d7.id.nbank.com_allnum");
        // 按身份证号查询近7天在非银机构持牌网络小贷机构申请次数
        JSON_PATH_MAPPERS.put("id_non_bank_nsloan_d7_cnt", "$.ApplyLoanStr.d7.id.nbank.nsloan_allnum");
        // 按身份证号查询近7天在非银机构持牌小贷机构申请次数
        JSON_PATH_MAPPERS.put("id_non_bank_sloan_d7_cnt", "$.ApplyLoanStr.d7.id.nbank.sloan_allnum");
        // 按身份证号查询近7天在非银机构持牌消费金融机构申请次数
        JSON_PATH_MAPPERS.put("id_non_bank_cons_d7_cnt", "$.ApplyLoanStr.d7.id.nbank.cons_allnum");
        // 按身份证号查询近7天在非银机构申请机构数
        JSON_PATH_MAPPERS.put("id_non_bank_d7_org", "$.ApplyLoanStr.d7.id.nbank.orgnum");
        // 按身份证号查询近7天在非银机构小贷申请机构数
        JSON_PATH_MAPPERS.put("id_non_bank_mc_d7_org", "$.ApplyLoanStr.d7.id.nbank.mc_orgnum");
        // 按身份证号查询近7天在非银机构现金类分期申请机构数
        JSON_PATH_MAPPERS.put("id_non_bank_cash_d7_org", "$.ApplyLoanStr.d7.id.nbank.ca_orgnum");
        // 按身份证号查询近7天在非银机构消费类分期申请机构数
        JSON_PATH_MAPPERS.put("id_non_bank_consume_d7_org", "$.ApplyLoanStr.d7.id.nbank.cf_orgnum");
        //按身份证号查询近7天在非银机构代偿类分期申请机构数
        JSON_PATH_MAPPERS.put("id_non_bank_repay_d7_org", "$.ApplyLoanStr.d7.id.nbank.com_orgnum");
        // 按身份证号查询近7天在非银机构持牌网络小贷机构申请机构数
        JSON_PATH_MAPPERS.put("id_non_bank_nsloan_d7_org", "$.ApplyLoanStr.d7.id.nbank.nsloan_orgnum");
        // 按身份证号查询近7天在非银机构持牌小贷机构申请机构数
        JSON_PATH_MAPPERS.put("id_non_bank_sloan_d7_org", "$.ApplyLoanStr.d7.id.nbank.sloan_orgnum");
        // 按身份证号查询近7天在非银机构持牌消费金融机构申请机构数
        JSON_PATH_MAPPERS.put("id_non_bank_cons_d7_org", "$.ApplyLoanStr.d7.id.nbank.cons_orgnum");
        // 按身份证号查询近7天在非银机构夜间申请次数
        JSON_PATH_MAPPERS.put("id_non_bank_night_d7_cnt", "$.ApplyLoanStr.d7.id.nbank.night_allnum");
        // 按身份证号查询近7天在非银机构夜间申请机构数
        JSON_PATH_MAPPERS.put("id_non_bank_night_d7_org", "$.ApplyLoanStr.d7.id.nbank.night_orgnum");
    }

