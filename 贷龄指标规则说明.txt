一、特征含义说明
1. 机构类型(orgtype)
- bank: 银行机构
- nbank: 非银机构
- cons: 消费金融机构
- fels: 互金机构
- isloan: 小贷机构
- oth: 其他机构

2. 经营方式(operation)
- ofu: 资金机构
- oas: 资产机构
- oth: 其他机构

3. 产品类型(bustype)
- cred: 信用卡产品
- syn: 联合贷产品
- caon: 线上现金贷产品
- coon: 线上消费贷产品
- pdl: 线上小额现金贷产品
- auto: 车贷产品
- cooff: 线下消费贷产品
- caoff: 线下现金贷产品
- hous: 房贷产品

4. 风险偏好(riskappetite)
- high: 高风险
- low: 低风险

二、命名规则说明
1. 字段命名规则
- 前缀: ala_(ApplyLoanAge简写)
- 查询类型: {type}(可替换为id/cell)
- 业务类型: 如bank/nbank等
- 后缀: first_days(首次进入距今天数)

2. JsonPath规则
- 基础路径: $.ApplyLoanAge.{type}
- 机构类型路径: .orgtype.xxx
- 经营方式路径: .operation.xxx
- 产品类型路径: .bustype.xxx
- 风险偏好路径: .riskappetite.xxx

三、示例说明
1. 机构类型示例
ala_{type}_bank_first_days: 首次进入银行借贷市场距今天数

2. 经营方式示例
ala_{type}_ofu_first_days: 首次进入资金机构借贷市场距今天数

3. 产品类型示例
ala_{type}_cred_first_days: 首次申请信用卡产品距今天数

4. 风险偏好示例
ala_{type}_risk_high_first_days: 首次进入高风险机构借贷市场距今天数 