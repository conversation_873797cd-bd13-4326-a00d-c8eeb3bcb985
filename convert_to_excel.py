#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows

def format_requirements(text):
    """将岗位要求文本格式化为1、2、3点形式"""
    # 按分号分割文本
    parts = text.split('；')
    formatted_parts = []
    for i, part in enumerate(parts, 1):
        if part.strip():  # 如果不是空字符串
            formatted_parts.append(f"{i}. {part.strip()}")
    return '\n'.join(formatted_parts)

def format_work_content(text):
    """将工作内容格式化为1、2、3点形式"""
    # 按分号分割文本
    parts = text.split('；')
    formatted_parts = []
    for i, part in enumerate(parts, 1):
        if part.strip():  # 如果不是空字符串
            formatted_parts.append(f"{i}. {part.strip()}")
    return '\n'.join(formatted_parts)

def create_recruitment_excel():
    """创建华中应急产业招聘信息Excel文件"""
    
    # 高级管理人员原始数据
    management_raw_data = [
        ["总经理", "全日制本科及以上学历，工商管理、经济、工程管理类相关专业；十年以上企业管理经验，具备中大型企业高管任职背景；熟悉企业经营管理、资本运作、项目管理；沟通协调能力强，具备战略视野与组织领导能力", "全面主持公司日常经营管理工作；组织实施董事会决议，制定公司经营计划和业务发展战略；协调公司各部门工作，推动组织高效运转；负责外部关系协调、重大合同签订、项目谈判等事务；组织建立健全公司管理制度与流程体系", "", "1"],
        ["总经办主任", "本科及以上学历，管理类相关专业；具备综合管理经验；具备较强的协调组织能力", "协助总经理开展公司日常经营管理工作", "", "1"],
        ["商务市场部总监", "市场营销、经济管理类专业本科及以上学历；五年以上商务管理或市场营销工作经验；熟悉B端市场拓展流程，有政府或大型企业资源者优先；较强的沟通、谈判及项目策划能力", "制定市场拓展策略与年度商务计划；负责客户开发、维护与客户满意度提升；组织商务谈判与合作协议签订；开发市场合作资源，打造企业品牌影响力", "", "1"],
        ["财务部总监", "财务、会计、审计相关专业本科及以上学历；具备注册会计师或高级会计职称优先；八年以上财务管理工作经验，熟悉税收法规与投融资业务；原则性强，具备风险控制意识", "建立健全公司财务管理制度；负责预算管理、税务筹划、成本控制；审核财务报表与资金运作情况；向董事会、管理层提供财务分析与决策建议", "", "1"],
        ["行政部总监", "行政管理、人力资源等相关专业本科以上学历；五年以上行政或综合管理经验；熟悉现代企业人事制度与绩效体系；高效执行与协调组织能力", "负责公司日常行政管理工作；建立企业文化和办公制度；管理人力资源体系，包括招聘、培训、绩效及员工关系；保障办公环境和后勤支持的高效运转", "", "1"],
        ["产业发展部总监", "经济、产业经济、区域发展、管理类等相关专业本科及以上学历；五年以上产业园区招商、运营或企业服务管理经验；熟悉政府政策、产业链发展规律及招商运营流程；具备较强的沟通协调能力、资源整合能力与文字策划能力", "编制公司产业发展战略、园区招商策划与产业规划方案；对接政府及产业资源，推动政策支持、项目落地与合作机制建立；组织实施园区招商引资、企业服务及运营模式建设工作；开展产业研究与项目评估；统筹管理产业合作平台、重大项目协调及部门团队建设", "", "1"],
        ["数智中心总监", "信息系统、计算机、数据科学等相关专业本科及以上学历；八年以上信息化或智能化系统管理经验，三年以上管理岗位经历；熟悉企业数字化转型、系统集成及数据平台架构；具备项目统筹、跨部门协调与团队管理能力", "制定公司数智化发展战略与年度工作计划；组织信息系统（ERP、OA、数据平台等）规划、建设与运维管理；建立公司统一的数据治理和信息安全管理体系；推动业务+数据+技术融合应用场景的落地实施；管理部门预算、团队建设及外部技术合作资源", "", "1"],
        ["工程部总监", "工程管理、土建等相关专业本科以上学历；十年以上工程项目管理经验；熟悉政府工程项目流程，具备一、二级建造师资格优先；抗压能力强，具有团队协调和应急处置能力", "负责公司工程项目全过程管理；编制与审核项目预算、成本计划；管控施工进度、安全与质量；组织工程验收、结算及审计对接", "", "1"],
        ["法务部总监", "法学专业本科以上学历，具备律师资格优先；五年以上法务工作经验；精通公司法、合同法及工程相关法律法规；谨慎细致，责任感强", "负责公司法律事务管理；审核合同文本、处理法律纠纷；风控体系建立与合规检查；为公司重大决策提供法律意见支持", "", "1"],
        ["采购部总监", "物流管理、供应链管理相关专业本科以上学历；五年以上采购管理经验；熟悉建筑、工程类物资采购管理；具备谈判能力、流程控制能力", "制定采购计划与流程规范；筛选、评估供应商，控制采购成本；采购合同管理及履约跟踪；与项目、财务部门协同保障采购及时性与合规性", "", "1"]
    ]
    
    # 一般员工数据
    staff_data = [
        ["商务经理", "市场营销、经济管理、项目管理类专业本科及以上学历；三年以上相关岗位经验，熟悉政府及企业采购流程；较强的市场敏感性、沟通协调及公文撰写能力；形象良好，责任心强，能接受出差及工作强度", "开展市场调研，收集行业、客户及竞争对手信息；参与制定市场开发策略，开拓业务渠道与合作资源；与客户开展业务洽谈，撰写商务方案，参与投标工作；跟踪商务合同执行，协调内外部资源促进项目实施；维护客户关系，提升客户满意度及复购率", "", "若干"],
        ["出纳", "会计、财务相关专业大专及以上学历；1年以上出纳工作经验，持有会计从业资格证；熟悉现金管理及银行结算业务流程；责任心强、保密意识强、细致严谨", "负责公司日常资金收付及银行结算业务；负责现金保管、票据管理、银行对账；配合会计进行凭证整理与账务处理；负责相关财务档案整理、登记现金日记账", "", "1"],
        ["会计", "会计、财务管理相关专业本科及以上学历；两年以上会计岗位经验，有施工企业或项目制企业经验者优先；熟悉国家财税政策，熟练使用财务软件；原则性强、具备数据分析能力和良好的职业操守", "编制会计凭证，进行账务处理，确保账实相符；出具财务报表，进行成本、费用、税收核算；按时申报各项税费，配合完成审计、年检等工作；协助制定财务预算及成本控制策略", "", "1"],
        ["行政专员", "行政管理、文秘相关专业大专及以上学历；2年以上行政事务工作经验；熟练使用办公软件，具备良好的文件管理及公文写作能力；工作细致、责任感强、服务意识强", "负责办公用品采购、设备维护与办公环境管理；协助组织公司会议、文件收发、制度执行等行政事务；管理公司印章、资质文件及档案资料；协助公司宣传、企业文化建设等工作", "", "1"],
        ["人力资源专员", "人力资源管理、心理学、行政管理类专业本科及以上学历；2年以上HR相关经验，熟悉招聘、绩效、培训等模块；熟悉劳动法、社保法等相关法律法规；具备良好的沟通协调能力和亲和力", "负责人员招聘、面试、录用与入职手续办理；组织员工培训、绩效考核、劳动合同管理；协调员工关系，推动企业文化落地；参与制定人力资源相关制度与薪酬体系", "", "1"],
        ["技术专员（产业发展部）", "工业工程、信息技术、智能制造、应急科技等相关专业本科及以上学历；两年以上相关技术咨询、科技园区或研发服务类工作经验；熟悉科技成果转化流程、项目技术评估、技术服务流程；具备良好的调研分析、材料撰写与沟通协调能力", "协助开展产业技术发展趋势调研与项目技术可行性分析；支持招商企业在智能制造、应急科技等方向的技术咨询与落地服务；撰写技术方案、可研报告与产业发展建议材料；协助组织产业对接、学术交流、路演推介等活动；协同部门完成产业数据库建设及信息资料归集整理", "", "若干"],
        ["工程师（数智中心）", "计算机、软件工程、信息技术等相关专业本科及以上学历；两年以上企业信息化或软件开发岗位工作经验；熟悉信息系统（如ERP、OA、BIM等）及常见数据库操作；具备良好的问题分析、技术实现与协作能力", "承担信息系统的日常运维与技术支持工作；参与软件系统、数据平台等功能模块的开发、测试与上线；支持数据采集、建模与可视化分析，提升业务决策效率；协助系统接口集成与智能设备（如无人机、监测平台）对接；落实网络与数据安全策略，处理技术故障与风险预警", "", "若干"],
        ["项管专员", "土木工程、建筑工程、项目管理类相关专业本科以上学历；三年以上工程现场管理经验，有施工单位或总包方经验优先；熟悉工程图纸、技术规范、施工流程", "负责项目现场施工组织协调、进度管理；监督施工质量、安全与文明施工；配合项目验收、竣工资料整理与归档；与甲方、设计、施工单位保持良好沟通", "", "若干"],
        ["预算专员", "工程造价、土木工程、工程管理等相关专业本科及以上学历；3年以上预算编制经验，熟悉清单计价规则及软件；具备一定项目现场沟通与协调能力", "参与项目招投标预算编制、合同预算管理；编制施工过程成本预算及结算资料；分析项目成本差异，为项目控制提供数据支持", "", "若干"],
        ["审计专员", "会计、审计、工程管理等相关专业本科及以上学历；两年以上工程审计、财务审计或造价审计经验；熟悉国家审计规范和建设行业审计流程", "审核项目财务支出、合同履约情况及工程量审核；实施内部控制审计、流程审计、专项审计；协助发现问题并提出整改建议，跟踪执行情况", "", "若干"],
        ["采购专员", "物流、采购、工程管理相关专业大专及以上学历；2年以上采购岗位经验，熟悉工程类采购管理；熟悉合同法、招标投标法等采购相关法规；工作细致、成本意识强", "执行采购计划，收集供应商报价并评估；审核采购合同，跟踪物资交付与到货验收；管理供应商档案与绩效，优化采购流程；协调采购相关部门需求，确保物资供应及时", "", "1"]
    ]
    
    # 合并所有数据
    all_data = management_data + staff_data
    
    # 创建DataFrame
    columns = ["岗位类别", "岗位要求", "工作内容", "薪资待遇", "招聘人数"]
    df = pd.DataFrame(all_data, columns=columns)
    
    # 创建工作簿
    wb = Workbook()
    ws = wb.active
    ws.title = "华中应急产业招聘信息"
    
    # 设置样式
    header_font = Font(name='宋体', size=12, bold=True, color='FFFFFF')
    header_fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')
    header_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    
    content_font = Font(name='宋体', size=10)
    content_alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)
    
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # 写入标题
    ws.merge_cells('A1:E1')
    ws['A1'] = '华中应急产业（湖北）有限公司招聘岗位信息表'
    ws['A1'].font = Font(name='宋体', size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center', vertical='center')
    
    # 写入高级管理人员标题
    ws.merge_cells('A2:E2')
    ws['A2'] = '高级管理人员'
    ws['A2'].font = Font(name='宋体', size=14, bold=True, color='FFFFFF')
    ws['A2'].fill = PatternFill(start_color='E74C3C', end_color='E74C3C', fill_type='solid')
    ws['A2'].alignment = Alignment(horizontal='center', vertical='center')
    
    # 写入表头
    for col, header in enumerate(columns, 1):
        cell = ws.cell(row=3, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = border
    
    # 写入高级管理人员数据
    for row_idx, row_data in enumerate(management_data, 4):
        for col_idx, value in enumerate(row_data, 1):
            cell = ws.cell(row=row_idx, column=col_idx, value=value)
            cell.font = content_font
            cell.alignment = content_alignment
            cell.border = border
    
    # 写入一般员工标题
    staff_start_row = 4 + len(management_data)
    ws.merge_cells(f'A{staff_start_row}:E{staff_start_row}')
    ws[f'A{staff_start_row}'] = '各部门员工岗位'
    ws[f'A{staff_start_row}'].font = Font(name='宋体', size=14, bold=True, color='FFFFFF')
    ws[f'A{staff_start_row}'].fill = PatternFill(start_color='27AE60', end_color='27AE60', fill_type='solid')
    ws[f'A{staff_start_row}'].alignment = Alignment(horizontal='center', vertical='center')
    
    # 写入一般员工表头
    staff_header_row = staff_start_row + 1
    for col, header in enumerate(columns, 1):
        cell = ws.cell(row=staff_header_row, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        cell.border = border
    
    # 写入一般员工数据
    staff_data_start_row = staff_header_row + 1
    for row_idx, row_data in enumerate(staff_data, staff_data_start_row):
        for col_idx, value in enumerate(row_data, 1):
            cell = ws.cell(row=row_idx, column=col_idx, value=value)
            cell.font = content_font
            cell.alignment = content_alignment
            cell.border = border
    
    # 设置列宽
    ws.column_dimensions['A'].width = 20
    ws.column_dimensions['B'].width = 50
    ws.column_dimensions['C'].width = 60
    ws.column_dimensions['D'].width = 15
    ws.column_dimensions['E'].width = 12
    
    # 设置行高
    for row in range(1, ws.max_row + 1):
        if row == 1:  # 标题行
            ws.row_dimensions[row].height = 30
        elif row in [2, staff_start_row]:  # 分类标题行
            ws.row_dimensions[row].height = 25
        elif row in [3, staff_header_row]:  # 表头行
            ws.row_dimensions[row].height = 40
        else:  # 数据行
            ws.row_dimensions[row].height = 120
    
    # 保存文件
    filename = '华中应急产业招聘岗位信息表.xlsx'
    wb.save(filename)
    print(f"Excel文件已生成：{filename}")
    
    return filename

if __name__ == "__main__":
    create_recruitment_excel()