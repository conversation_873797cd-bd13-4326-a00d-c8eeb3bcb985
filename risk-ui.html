<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>风险报告 - Risk Report</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f0f2f5;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }
        .report-container {
            width: 800px;
            background-color: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .report-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #e0e0e0;
        }
        .report-title {
            display: flex;
            align-items: center;
        }
        .report-title h1 {
            color: #1a73e8;
            font-size: 24px;
            margin-right: 10px;
        }
        .report-title small {
            color: #666;
        }
        .risk-stamp {
            background-color: #ff4d4f;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-weight: bold;
        }
        .report-info {
            display: flex;
            padding: 15px;
        }
        .user-profile {
            display: flex;
            align-items: center;
            margin-right: 20px;
        }
        .user-icon {
            width: 50px;
            height: 50px;
            background-color: #1a73e8;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            margin-right: 10px;
        }
        .user-details {
            display: flex;
            flex-direction: column;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
        }
        .info-item {
            display: flex;
            flex-direction: column;
            border: 1px solid #e0e0e0;
            padding: 10px;
            border-radius: 4px;
        }
        .info-item-title {
            color: #666;
            font-size: 12px;
            margin-bottom: 5px;
        }
        .info-item-value {
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="report-container">
        <div class="report-header">
            <div class="report-title">
                <h1>风险报告</h1>
                <small>RISK REPORT</small>
            </div>
            <div class="risk-stamp">建议拒绝</div>
        </div>
        <div class="report-info">
            <div class="user-profile">
                <div class="user-icon">吴**</div>
                <div class="user-details">
                    <div>吴**</div>
                    <div>150**** ****32</div>
                </div>
            </div>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-item-title">申请人银行卡号</div>
                    <div class="info-item-value">6230**** ****343</div>
                </div>
                <div class="info-item">
                    <div class="info-item-title">业务产品版本</div>
                    <div class="info-item-value">V1</div>
                </div>
                <div class="info-item">
                    <div class="info-item-title">业务产品版本</div>
                    <div class="info-item-value">V1</div>
                </div>
                <div class="info-item">
                    <div class="info-item-title">申请人证件号码</div>
                    <div class="info-item-value">42052* **** ****49</div>
                </div>
                <div class="info-item">
                    <div class="info-item-title">业务产品名称</div>
                    <div class="info-item-value">完整流程测试</div>
                </div>
                <div class="info-item">
                    <div class="info-item-title">业务产品名称</div>
                    <div class="info-item-value">完整流程测试</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>