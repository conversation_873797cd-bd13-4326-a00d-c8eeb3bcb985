TEMPLATE_TABLE.put("ala_{type}_first_days", "$.ApplyLoanAge.{type}.tot", "根据{typeDesc}查询预测用户首次进入借贷市场距今天数");
TEMPLATE_TABLE.put("ala_{type}_bank_first_days", "$.ApplyLoanAge.{type}.orgtype.bank", "根据{typeDesc}查询预测用户首次进入银行借贷市场距今天数");
TEMPLATE_TABLE.put("ala_{type}_nbank_first_days", "$.ApplyLoanAge.{type}.orgtype.nbank", "根据{typeDesc}查询预测用户首次进入非银机构借贷市场距今天数");
TEMPLATE_TABLE.put("ala_{type}_cons_first_days", "$.ApplyLoanAge.{type}.orgtype.cons", "根据{typeDesc}查询预测用户首次进入消费金融借贷市场距今天数");
TEMPLATE_TABLE.put("ala_{type}_fels_first_days", "$.ApplyLoanAge.{type}.orgtype.fels", "根据{typeDesc}查询预测用户首次进入互金机构借贷市场距今天数");
TEMPLATE_TABLE.put("ala_{type}_isloan_first_days", "$.ApplyLoanAge.{type}.orgtype.isloan", "根据{typeDesc}查询预测用户首次进入机构类型为小贷的借贷市场的距今天数");
TEMPLATE_TABLE.put("ala_{type}_other_first_days", "$.ApplyLoanAge.{type}.orgtype.oth", "根据{typeDesc}查询预测用户首次进入机构类型为其他的机构的借贷市场的距今天数");
TEMPLATE_TABLE.put("ala_{type}_ofu_first_days", "$.ApplyLoanAge.{type}.operation.ofu", "根据{typeDesc}查询预测用户首次进入经营方式为资金机构的借贷市场的距今天数");
TEMPLATE_TABLE.put("ala_{type}_oas_first_days", "$.ApplyLoanAge.{type}.operation.oas", "根据{typeDesc}查询预测用户首次进入经营方式为资产机构的借贷市场的距今天数");
TEMPLATE_TABLE.put("ala_{type}_other_first_days", "$.ApplyLoanAge.{type}.operation.oth", "根据{typeDesc}查询预测用户首次进入经营方式为其他机构的借贷市场的距今天数");
TEMPLATE_TABLE.put("ala_{type}_cred_first_days", "$.ApplyLoanAge.{type}.bustype.cred", "根据{typeDesc}查询预测用户首次申请产品类型为信用卡产品的借贷市场的距今天数");
TEMPLATE_TABLE.put("ala_{type}_syn_first_days", "$.ApplyLoanAge.{type}.bustype.syn", "根据{typeDesc}查询预测用户首次申请产品类型为联合贷产品的借贷市场的距今天数");
TEMPLATE_TABLE.put("ala_{type}_caon_first_days", "$.ApplyLoanAge.{type}.bustype.caon", "根据{typeDesc}查询预测用户首次申请产品类型为线上现金贷产品的借贷市场的距今天数");
TEMPLATE_TABLE.put("ala_{type}_coon_first_days", "$.ApplyLoanAge.{type}.bustype.coon", "根据{typeDesc}查询预测用户首次申请产品类型为线上消费贷产品的借贷市场的距今天数");
TEMPLATE_TABLE.put("ala_{type}_pdl_first_days", "$.ApplyLoanAge.{type}.bustype.pdl", "根据{typeDesc}查询预测用户首次申请产品类型为线上小额现金贷产品的借贷市场的距今天数");
TEMPLATE_TABLE.put("ala_{type}_auto_first_days", "$.ApplyLoanAge.{type}.bustype.auto", "根据{typeDesc}查询预测用户首次申请产品类型为车贷产品的借贷市场的距今天数");
TEMPLATE_TABLE.put("ala_{type}_cooff_first_days", "$.ApplyLoanAge.{type}.bustype.cooff", "根据{typeDesc}查询预测用户首次申请产品类型为线下消费贷产品的借贷市场的距今天数");
TEMPLATE_TABLE.put("ala_{type}_caoff_first_days", "$.ApplyLoanAge.{type}.bustype.caoff", "根据{typeDesc}查询预测用户首次申请产品类型为线下现金贷产品的借贷市场的距今天数");
TEMPLATE_TABLE.put("ala_{type}_house_first_days", "$.ApplyLoanAge.{type}.bustype.hous", "根据{typeDesc}查询预测用户首次申请产品类型为房贷产品的借贷市场的距今天数");
TEMPLATE_TABLE.put("ala_{type}_risk_high_first_days", "$.ApplyLoanAge.{type}.riskappetite.high", "根据{typeDesc}查询预测用户首次进入风险偏好为高风险的机构的借贷市场的距今天数");
TEMPLATE_TABLE.put("ala_{type}_risk_low_first_days", "$.ApplyLoanAge.{type}.riskappetite.low", "根据{typeDesc}查询预测用户首次进入风险偏好为低风险的机构的借贷市场的距今天数");
