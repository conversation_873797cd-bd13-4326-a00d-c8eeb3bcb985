SELECT
	`t1`.`batch_date` AS `batch_date`,
	`t2`.`mchnt_no` AS `mchnt_no`,
	`t2`.`mchnt_name` AS `mchnt_name`,
	`t2`.`mchnt_simple_name` AS `mchnt_simple_name`,
	`t1`.`term_no` AS `term_no`,
	substr( `t5`.`front_seq_num`, 24, 6 ) AS `term_batchno`,
	substr( `t5`.`front_seq_num`, 30, 6 ) AS `term_serialno`,
	`t1`.`trans_date` AS `trans_date`,
	`t1`.`trans_time` AS `trans_time`,
	`t7`.`txn_name` AS `txn_name`,
	`t1`.`order_id` AS `order_id`,
	`t1`.`trans_amt` AS `trans_amt`,
	`t6`.`order_desc` AS `order_desc`,
	`t1`.`pay_acct_type` AS `pay_acct_type`,
	`t1`.`pay_acct_no` AS `pay_acct_no`,
	`t1`.`mchnt_actual_fee` AS `mchnt_actual_fee`,
	`t3`.`marketing_amt` AS `marketing_amt`,
	round(( `t1`.`mchnt_actual_fee` - `t3`.`marketing_amt` ), 2 ) AS `receiptsfee`,
	`t4`.`acct_date` AS `acct_date`,
	`t8`.`txn_date` AS `txn_date`,
	`t8`.`txn_time` AS `txn_time`,
	`t8`.`mchnt_order_id` AS `mchnt_order_id`,
	`t8`.`order_amt` AS `order_amt` 
FROM
	(((((((((
									SELECT
										`csmcdb`.`t_c_mchnt_acct_detail`.`inst_id` AS `inst_id`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`batch_date` AS `batch_date`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`task_param` AS `task_param`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`batch_no` AS `batch_no`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`atom_task_id` AS `atom_task_id`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`biz_prod_type` AS `biz_prod_type`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`primary_key` AS `primary_key`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`orig_key` AS `orig_key`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`txn_num` AS `txn_num`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`csmc_biz_type` AS `csmc_biz_type`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`instruction_id` AS `instruction_id`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`mchnt_no` AS `mchnt_no`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`group_mchnt_no` AS `group_mchnt_no`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`plat_mchnt_no` AS `plat_mchnt_no`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`agent_mchnt_no` AS `agent_mchnt_no`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`chain_mchnt_no` AS `chain_mchnt_no`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`settle_flag` AS `settle_flag`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`real_settle_flag` AS `real_settle_flag`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`real_settle_status` AS `real_settle_status`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`term_type` AS `term_type`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`term_no` AS `term_no`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`user_id` AS `user_id`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`trans_seq_num` AS `trans_seq_num`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`order_id` AS `order_id`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`trans_date` AS `trans_date`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`trans_time` AS `trans_time`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`pay_acct_no` AS `pay_acct_no`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`pay_acct_type` AS `pay_acct_type`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`trans_amt` AS `trans_amt`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`real_pay_amt` AS `real_pay_amt`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`bank_subsidy_amt` AS `bank_subsidy_amt`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`mchnt_subsidy_amt` AS `mchnt_subsidy_amt`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`mchnt_net_amt` AS `mchnt_net_amt`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`mchnt_total_fee` AS `mchnt_total_fee`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`mchnt_actual_fee` AS `mchnt_actual_fee`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`mchnt_extra_fee` AS `mchnt_extra_fee`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`mchnt_discount_fee` AS `mchnt_discount_fee`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`mchnt_divide_fee` AS `mchnt_divide_fee`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`total_refund_amt` AS `total_refund_amt`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`total_refund_fee` AS `total_refund_fee`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`vir_acct_trans_amt` AS `vir_acct_trans_amt`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`acq_inst_code` AS `acq_inst_code`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`trans_channel_id` AS `trans_channel_id`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`pay_channel_id` AS `pay_channel_id`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`net_settle_date` AS `net_settle_date`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`fee_settle_date` AS `fee_settle_date`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`other_biz_info` AS `other_biz_info`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`create_time` AS `create_time`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`update_time` AS `update_time`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`remark1` AS `remark1`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`remark2` AS `remark2`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`remark3` AS `remark3`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`remark4` AS `remark4`,
										`csmcdb`.`t_c_mchnt_acct_detail`.`remark5` AS `remark5` 
									FROM
										`csmcdb`.`t_c_mchnt_acct_detail` 
									WHERE
										((
												1 = 1 
												) 
											AND ( `csmcdb`.`t_c_mchnt_acct_detail`.`batch_date` = '20220111' ) 
										AND ( `csmcdb`.`t_c_mchnt_acct_detail`.`mchnt_no` = '000210859451112' )))) `t1`
								JOIN `upmpdb`.`t_b_mchnt_base_info` `t2` ON ((
										`t1`.`mchnt_no` = `t2`.`mchnt_no` 
									)))
							LEFT JOIN (
							SELECT
								sum( `csmcdb`.`t_c_mcc_marketing_det`.`marketing_amt` ) AS `marketing_amt`,
								`csmcdb`.`t_c_mcc_marketing_det`.`primary_key` AS `primary_key`,
								`csmcdb`.`t_c_mcc_marketing_det`.`mchnt_no` AS `mchnt_no`,
								`csmcdb`.`t_c_mcc_marketing_det`.`batch_date` AS `batch_date` 
							FROM
								`csmcdb`.`t_c_mcc_marketing_det` 
							WHERE
								((
										1 = 1 
										) 
									AND ( `csmcdb`.`t_c_mcc_marketing_det`.`batch_date` = '20220111' ) 
								AND ( `csmcdb`.`t_c_mcc_marketing_det`.`mchnt_no` = '000210859451112' )) 
							GROUP BY
								`csmcdb`.`t_c_mcc_marketing_det`.`primary_key`,
								`csmcdb`.`t_c_mcc_marketing_det`.`mchnt_no`,
								`csmcdb`.`t_c_mcc_marketing_det`.`batch_date` 
								) `t3` ON (((
										`t1`.`primary_key` = `t3`.`primary_key` 
										) 
									AND ( `t1`.`mchnt_no` = `t3`.`mchnt_no` ) 
								AND ( `t1`.`batch_date` = `t3`.`batch_date` ))))
						LEFT JOIN `csmcdb`.`t_c_mchnt_day_statis` `t4` ON (((
									`t4`.`mchnt_no` = `t1`.`mchnt_no` 
									) 
							AND ( `t1`.`batch_date` = `t4`.`batch_date` ))))
					LEFT JOIN `oltpdb`.`t_p_user_order_info` `t5` ON (((
								`t5`.`mchnt_order_id` = `t1`.`order_id` 
								) 
						AND ( `t5`.`trans_channel_id` = 'NPOS' ))))
				LEFT JOIN `oltpdb`.`t_p_mchnt_order_info` `t6` ON ((
						`t6`.`mchnt_order_id` = `t1`.`order_id` 
					)))
			LEFT JOIN (
			SELECT
				`oltpdb`.`t_p_txn_cfg`.`txn_num` AS `txn_num`,
				`oltpdb`.`t_p_txn_cfg`.`biz_type` AS `biz_type`,
				`oltpdb`.`t_p_txn_cfg`.`biz_prod_code` AS `biz_prod_code`,
				`oltpdb`.`t_p_txn_cfg`.`txn_name` AS `txn_name` 
			FROM
				`oltpdb`.`t_p_txn_cfg` 
			GROUP BY
				`oltpdb`.`t_p_txn_cfg`.`txn_num`,
				`oltpdb`.`t_p_txn_cfg`.`biz_type`,
				`oltpdb`.`t_p_txn_cfg`.`biz_prod_code` 
				) `t7` ON (((
						`t7`.`txn_num` = `t6`.`txn_num` 
						) 
					AND ( `t7`.`biz_type` = `t6`.`biz_type` ) 
				AND ( `t7`.`biz_prod_code` = `t6`.`biz_prod_code` ))))
		LEFT JOIN `oltpdb`.`t_p_mchnt_order_info` `t8` ON (((
					`t8`.`txn_date` = `t6`.`orig_txn_date` 
				) 
	AND ( `t8`.`sys_seq_num` = `t6`.`orig_sys_seq_num` ))))