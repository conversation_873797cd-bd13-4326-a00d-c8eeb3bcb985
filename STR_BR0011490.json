{"code": "00", "RiskStrategy": {"strategy_version": "1.1", "product_type": "100099", "final_decision": "Reject", "strategy_id": "STR_BR0011490", "Rule": {"decision": "Reject", "weight": "100"}, "product_name": "预置_特殊名单验证", "platform": "web", "scene": "lend"}, "swift_number": "3034679_20250102115150_5145", "Rule": {"result": {"final_weight": "100", "final_decision": "Reject"}, "hit_rules": {"rulespeciallist_c": {"odr0000335": {"weight": "100", "name_rule": "近两年命中银行高风险"}, "odr0000346": {"weight": "25", "name_rule": "两年前命中非银一般风险"}, "odr0000336": {"weight": "95", "name_rule": "两年前命中银行高风险"}, "odr0000347": {"weight": "85", "name_rule": "命中银行中风险次数较多"}, "odr0000337": {"weight": "80", "name_rule": "近两年命中银行中风险"}, "odr0000348": {"weight": "35", "name_rule": "命中银行一般风险次数较多"}, "odr0000338": {"weight": "75", "name_rule": "两年前命中银行中风险"}, "odr0000349": {"weight": "85", "name_rule": "命中非银中风险次数较多"}, "odr0000339": {"weight": "30", "name_rule": "近两年命中银行一般风险"}, "odr0000350": {"weight": "35", "name_rule": "命中非银一般风险次数较多"}, "odr0000340": {"weight": "25", "name_rule": "两年前命中银行一般风险"}, "odr0000341": {"weight": "100", "name_rule": "近两年命中非银高风险"}, "odr0000331": {"weight": "100", "name_rule": "近两年命中法院失信人"}, "odr0000342": {"weight": "95", "name_rule": "两年前命中非银高风险"}, "odr0000332": {"weight": "90", "name_rule": "两年前命中法院失信人"}, "odr0000343": {"weight": "80", "name_rule": "近两年命中非银中风险"}, "odr0000333": {"weight": "80", "name_rule": "近两年命中法院被执行人"}, "odr0000344": {"weight": "75", "name_rule": "两年前命中非银中风险"}, "odr0000334": {"weight": "70", "name_rule": "两年前命中法院被执行人"}, "odr0000345": {"weight": "30", "name_rule": "近两年命中非银一般风险"}}, "ruleexecutionlimited": {"odr0000210": {"weight": "80", "name_rule": "为法院失信被执行人"}, "odr0000211": {"weight": "80", "name_rule": "为法院失信被执行人已下架"}, "odr0000212": {"weight": "80", "name_rule": "为法院限高被执行人"}, "odr0000213": {"weight": "80", "name_rule": "为法院限高被执行人已下架"}}}}, "SpecialList_c": {"id": {"court_executed_time": "1", "nbank_lost_allnum": "1", "nbank_sloan_bad": "0", "nbank_autofin_overdue_allnum": "8", "nbank_other_bad": "0", "nbank_cons_lost_time": "1", "nbank_other_lost_allnum": "2", "nbank_finlea_lost": "0", "nbank_other_overdue_allnum": "7", "bank_lost_allnum": "5", "nbank_nsloan_bad_time": "0", "nbank_bad_time": "0", "nbank_lost": "0", "nbank_sloan_bad_time": "0", "nbank_autofin_overdue_time": "0", "nbank_nsloan_overdue": "0", "nbank_nsloan_lost_time": "1", "nbank_overdue_allnum": "1", "nbank_overdue": "0", "nbank_other_overdue_time": "0", "nbank_finlea_bad": "0", "bank_bad_allnum": "1", "nbank_cons_lost": "0", "nbank_nsloan_bad": "0", "bank_lost": "0", "nbank_sloan_lost_time": "1", "nbank_other_bad_time": "0", "bank_bad_time": "1", "nbank_autofin_lost_allnum": "2", "court_bad_time": "0", "nbank_cons_overdue_time": "0", "nbank_sloan_overdue_allnum": "1", "bank_bad": "0", "nbank_finlea_overdue_time": "2", "nbank_nsloan_bad_allnum": "7", "court_bad": "0", "nbank_cons_bad_time": "1", "nbank_finlea_bad_time": "1", "nbank_sloan_overdue": "0", "nbank_sloan_lost_allnum": "1", "nbank_cons_lost_allnum": "1", "nbank_cons_overdue": "0", "bank_overdue": "0", "nbank_bad_allnum": "6", "court_bad_allnum": "1", "court_executed_allnum": "1", "nbank_sloan_lost": "0", "court_executed": "0", "nbank_nsloan_lost": "0", "bank_lost_time": "0", "nbank_autofin_lost_time": "2", "nbank_cons_bad": "0", "nbank_finlea_lost_time": "0", "nbank_autofin_overdue": "0", "nbank_sloan_bad_allnum": "1", "nbank_other_overdue": "0", "nbank_sloan_overdue_time": "1", "nbank_other_bad_allnum": "5", "nbank_autofin_bad": "0", "nbank_autofin_lost": "0", "nbank_finlea_overdue_allnum": "2", "nbank_cons_bad_allnum": "1", "bank_overdue_allnum": "2", "nbank_lost_time": "1", "nbank_autofin_bad_time": "1", "nbank_nsloan_lost_allnum": "1", "nbank_other_lost": "0", "nbank_bad": "0", "bank_overdue_time": "2", "nbank_autofin_bad_allnum": "1", "nbank_nsloan_overdue_time": "1", "nbank_other_lost_time": "0", "nbank_cons_overdue_allnum": "9", "nbank_finlea_lost_allnum": "4", "nbank_finlea_overdue": "0", "nbank_overdue_time": "1", "nbank_finlea_bad_allnum": "1", "nbank_nsloan_overdue_allnum": "1"}, "cell": {"nbank_lost_allnum": "1", "nbank_sloan_bad": "0", "nbank_autofin_overdue_allnum": "6", "nbank_other_bad": "0", "nbank_cons_lost_time": "1", "nbank_other_lost_allnum": "9", "nbank_finlea_lost": "0", "nbank_other_overdue_allnum": "6", "bank_lost_allnum": "3", "nbank_nsloan_bad_time": "0", "nbank_bad_time": "0", "nbank_lost": "0", "nbank_sloan_bad_time": "0", "nbank_autofin_overdue_time": "0", "nbank_nsloan_overdue": "0", "nbank_nsloan_lost_time": "1", "nbank_overdue_allnum": "1", "nbank_overdue": "0", "nbank_other_overdue_time": "0", "nbank_finlea_bad": "0", "bank_bad_allnum": "1", "nbank_cons_lost": "0", "nbank_nsloan_bad": "0", "bank_lost": "0", "nbank_sloan_lost_time": "1", "nbank_other_bad_time": "0", "bank_bad_time": "1", "nbank_autofin_lost_allnum": "2", "nbank_cons_overdue_time": "0", "nbank_sloan_overdue_allnum": "1", "bank_bad": "0", "nbank_finlea_overdue_time": "2", "nbank_nsloan_bad_allnum": "3", "nbank_cons_bad_time": "1", "nbank_finlea_bad_time": "1", "nbank_sloan_overdue": "0", "nbank_sloan_lost_allnum": "1", "nbank_cons_lost_allnum": "1", "nbank_cons_overdue": "0", "bank_overdue": "0", "nbank_bad_allnum": "5", "nbank_sloan_lost": "0", "nbank_nsloan_lost": "0", "bank_lost_time": "0", "nbank_autofin_lost_time": "2", "nbank_cons_bad": "0", "nbank_finlea_lost_time": "0", "nbank_autofin_overdue": "0", "nbank_sloan_bad_allnum": "7", "nbank_other_overdue": "0", "nbank_sloan_overdue_time": "1", "nbank_other_bad_allnum": "7", "nbank_autofin_bad": "0", "nbank_autofin_lost": "0", "nbank_finlea_overdue_allnum": "2", "nbank_cons_bad_allnum": "1", "bank_overdue_allnum": "2", "nbank_lost_time": "1", "nbank_autofin_bad_time": "1", "nbank_nsloan_lost_allnum": "1", "nbank_other_lost": "0", "nbank_bad": "0", "bank_overdue_time": "2", "nbank_autofin_bad_allnum": "1", "nbank_nsloan_overdue_time": "1", "nbank_other_lost_time": "0", "nbank_cons_overdue_allnum": "8", "nbank_finlea_lost_allnum": "4", "nbank_finlea_overdue": "0", "nbank_overdue_time": "1", "nbank_finlea_bad_allnum": "1", "nbank_nsloan_overdue_allnum": "1"}}, "Flag": {"riskstrategy": "1", "executionlimited": "1", "specialList_c": "1", "rulespeciallist_c": "1", "ruleexecutionlimited": "1"}, "ExecutionLimited": {"xg2": {"casecode": "  (2018)内0105执2656号", "areaname": "内蒙古", "sexname": "女", "datatype": "限高被执行人", "courtname": "呼和浩特市赛罕区人民法院", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "regdate": "2018-06-01", "sign": "0", "publishdate": "2018-06-01", "signalRating": "1", "age": "38", "iname": "荣珍"}, "xg1": {"casecode": "  (2018)内0105执2656号", "areaname": "内蒙古", "sexname": "女", "datatype": "限高被执行人", "courtname": "呼和浩特市赛罕区人民法院", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "regdate": "2018-06-01", "sign": "0", "publishdate": "2018-06-01", "signalRating": "1", "age": "38", "iname": "荣珍"}, "xg4": {"casecode": "  (2018)内0105执2656号", "areaname": "内蒙古", "sexname": "女", "datatype": "限高被执行人", "courtname": "呼和浩特市赛罕区人民法院", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "regdate": "2018-06-01", "sign": "0", "publishdate": "2018-06-01", "signalRating": "1", "age": "38", "iname": "荣珍"}, "xg3": {"casecode": "  (2018)内0105执2656号", "areaname": "内蒙古", "sexname": "女", "datatype": "限高被执行人", "courtname": "呼和浩特市赛罕区人民法院", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "regdate": "2018-06-01", "sign": "0", "publishdate": "2018-06-01", "signalRating": "1", "age": "38", "iname": "荣珍"}, "xg6": {"casecode": "  (2018)内0105执2656号", "areaname": "内蒙古", "sexname": "女", "datatype": "限高被执行人", "courtname": "呼和浩特市赛罕区人民法院", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "regdate": "2018-06-01", "sign": "0", "publishdate": "2018-06-01", "signalRating": "1", "age": "38", "iname": "荣珍"}, "xg5": {"casecode": "  (2018)内0105执2656号", "areaname": "内蒙古", "sexname": "女", "datatype": "限高被执行人", "courtname": "呼和浩特市赛罕区人民法院", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "regdate": "2018-06-01", "sign": "0", "publishdate": "2018-06-01", "signalRating": "1", "age": "38", "iname": "荣珍"}, "xg8": {"casecode": "  (2018)内0105执2656号", "areaname": "内蒙古", "sexname": "女", "datatype": "限高被执行人", "courtname": "呼和浩特市赛罕区人民法院", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "regdate": "2018-06-01", "sign": "0", "publishdate": "2018-06-01", "signalRating": "1", "age": "38", "iname": "荣珍"}, "xg7": {"casecode": "  (2018)内0105执2656号", "areaname": "内蒙古", "sexname": "女", "datatype": "限高被执行人", "courtname": "呼和浩特市赛罕区人民法院", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "regdate": "2018-06-01", "sign": "0", "publishdate": "2018-06-01", "signalRating": "1", "age": "38", "iname": "荣珍"}, "xg9": {"casecode": "  (2018)内0105执2656号", "areaname": "内蒙古", "sexname": "女", "datatype": "限高被执行人", "courtname": "呼和浩特市赛罕区人民法院", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "regdate": "2018-06-01", "sign": "0", "publishdate": "2018-06-01", "signalRating": "1", "age": "38", "iname": "荣珍"}, "sx2": {"gistcid": "（2012）历城民商初字第883号", "areaname": "内蒙古", "sexname": "女", "courtname": "呼和浩特市赛罕区人民法院", "sign": "0", "buesinessentity": "", "iname": "荣珍", "casecode": "  (2018)内0105执2656号", "datatype": "失信被执行人", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "regdate": "2018-06-01", "publishdate": "2018-06-01", "signalRating": "1", "age": "38", "partytypename": "0"}, "sx1": {"gistcid": "（2012）历城民商初字第883号", "areaname": "内蒙古", "sexname": "女", "courtname": "呼和浩特市赛罕区人民法院", "sign": "0", "buesinessentity": "", "iname": "荣珍", "casecode": "  (2018)内0105执2656号", "datatype": "失信被执行人", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "regdate": "2018-06-01", "publishdate": "2018-06-01", "signalRating": "1", "age": "38", "partytypename": "0"}, "sx4": {"gistcid": "（2012）历城民商初字第883号", "areaname": "内蒙古", "sexname": "女", "courtname": "呼和浩特市赛罕区人民法院", "sign": "0", "buesinessentity": "", "iname": "荣珍", "casecode": "  (2018)内0105执2656号", "datatype": "失信被执行人", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "regdate": "2018-06-01", "publishdate": "2018-06-01", "signalRating": "1", "age": "38", "partytypename": "0"}, "sx3": {"gistcid": "（2012）历城民商初字第883号", "areaname": "内蒙古", "sexname": "女", "courtname": "呼和浩特市赛罕区人民法院", "sign": "0", "buesinessentity": "", "iname": "荣珍", "casecode": "  (2018)内0105执2656号", "datatype": "失信被执行人", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "regdate": "2018-06-01", "publishdate": "2018-06-01", "signalRating": "1", "age": "38", "partytypename": "0"}, "sx6": {"gistcid": "（2012）历城民商初字第883号", "areaname": "内蒙古", "sexname": "女", "courtname": "呼和浩特市赛罕区人民法院", "sign": "0", "buesinessentity": "", "iname": "荣珍", "casecode": "  (2018)内0105执2656号", "datatype": "失信被执行人", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "regdate": "2018-06-01", "publishdate": "2018-06-01", "signalRating": "1", "age": "38", "partytypename": "0"}, "sx5": {"gistcid": "（2012）历城民商初字第883号", "areaname": "内蒙古", "sexname": "女", "courtname": "呼和浩特市赛罕区人民法院", "sign": "0", "buesinessentity": "", "iname": "荣珍", "casecode": "  (2018)内0105执2656号", "datatype": "失信被执行人", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "regdate": "2018-06-01", "publishdate": "2018-06-01", "signalRating": "1", "age": "38", "partytypename": "0"}, "sx8": {"gistcid": "（2012）历城民商初字第883号", "areaname": "内蒙古", "sexname": "女", "courtname": "呼和浩特市赛罕区人民法院", "sign": "0", "buesinessentity": "", "iname": "荣珍", "casecode": "  (2018)内0105执2656号", "datatype": "失信被执行人", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "regdate": "2018-06-01", "publishdate": "2018-06-01", "signalRating": "1", "age": "38", "partytypename": "0"}, "sx10": {"gistcid": "（2012）历城民商初字第883号", "areaname": "内蒙古", "sexname": "女", "courtname": "呼和浩特市赛罕区人民法院", "sign": "0", "buesinessentity": "", "iname": "荣珍", "casecode": "  (2018)内0105执2656号", "datatype": "失信被执行人", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "regdate": "2018-06-01", "publishdate": "2018-06-01", "signalRating": "1", "age": "38", "partytypename": "0"}, "sx7": {"gistcid": "（2012）历城民商初字第883号", "areaname": "内蒙古", "sexname": "女", "courtname": "呼和浩特市赛罕区人民法院", "sign": "0", "buesinessentity": "", "iname": "荣珍", "casecode": "  (2018)内0105执2656号", "datatype": "失信被执行人", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "regdate": "2018-06-01", "publishdate": "2018-06-01", "signalRating": "1", "age": "38", "partytypename": "0"}, "sx9": {"gistcid": "（2012）历城民商初字第883号", "areaname": "内蒙古", "sexname": "女", "courtname": "呼和浩特市赛罕区人民法院", "sign": "0", "buesinessentity": "", "iname": "荣珍", "casecode": "  (2018)内0105执2656号", "datatype": "失信被执行人", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "regdate": "2018-06-01", "publishdate": "2018-06-01", "signalRating": "1", "age": "38", "partytypename": "0"}, "xg10": {"casecode": "  (2018)内0105执2656号", "areaname": "内蒙古", "sexname": "女", "datatype": "限高被执行人", "courtname": "呼和浩特市赛罕区人民法院", "signalDesc": "主体被拉入失信名单，且发生在三年之内", "regdate": "2018-06-01", "sign": "0", "publishdate": "2018-06-01", "signalRating": "1", "age": "38", "iname": "荣珍"}}}